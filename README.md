# redis-shake

A tool used to sync data from peers, based on [RedisShake](https://github.com/alibaba/RedisShake) v3, most are refactored. 

## Resources

- [Documentation](https://cooper.didichuxing.com/knowledge/share/page/wnxpHPLCs3qU)
- [RedisShake](https://github.com/alibaba/RedisShake)
- [go-redis](https://github.com/go-redis/redis)

## Installation

Firstly, get the code from gitlab.
```
$ <NAME_EMAIL>:kvdb/redis-syncer.git
 
$ cd redis-syncer
$ sh build.sh
```
Note that, it requires **golang 1.18**, with modules support.

Then, compile it, the result will place into dir output.
```
output
├── bin
│   └── redis-syncer
├── conf
│    └── redis-syncer.toml
├── control.sh
└── log
```

And, run it, using default conf.
```
$ cd output
$ sh control.sh start
```

We can get version info using option '-v', like this,
```
$ ./redis-syncer -v
redis-syncer v=1.0 sha=9ecb8092:0 bits=64 build=5263713994600518895
build time: Thu Oct 13 20:18:14 2022 +0800
```

## API supported 

- Re-balance sync tasks on the server.

```
PUT /api/v1/tasks

{
    "dst_addr":"127.0.0.1:7777", // required
    "dst_username": "xxx",
    "dst_password": "xxxx"
    "dst_is_tls": false,
    "dst_cluster": " hna-v.aiot-kproxy.chezai" // required
   
    "src_username": "xxx",
    "src_password": "xxxx"
    "src_is_tls": false,
    "src_cluster": " hna-v.aiot-kproxy.chezai", // required
   
    "rdb_file_path"： "xxx", // used in restore type
    "not_colorize": false, // colorize rdb entry or not
    
    "type": "psync", // support psync/restore as far, default is psync
 
    "shards":[ // required
        {
            "shard_id": 1234567,
            "group_id": 1,
            "master":"xxxx:6000", // ip:port
            "slave":"xxxxx:6003" // ip:port
            "need_rdb": 1, 
        }
    ]
}
```

- Get all syncers running on the server.
```
GET /api/v1/tasks
{
    "errno": 200,
    "errmsg": "ok",
    "data": [
        {
            "syncer_id": "hnb-v.src#hna-v.dst#0", // 任务 id
            "read_repl_off": 273062, // 读到的 offset
            "repl_off": 273062, // 写成功的 offset
            "read_opid": 1350, // 读到的 opid
            "opid": 1350, // 写成功的 opid
            "master": "************:7000",
            "slaves": [
                "************:7004"
            ],
            "rdb_path": "",  // 加载的 rdb 文件， 用于 restore 任务
            "type": "psync", // 任务类型
            "start_at": "2024-04-25T17:30:25.385718+08:00",  // 启动时间
            "need_rdb": 1, // 是否需要 rdb
            "status": "online", // 任务状态 online/offline
            "rstage": "psync-send-aof", // reader 状态
            "wstage": "send-aof" // writer 状态
        }
    ]
}
```
reader 状态取值如下，
```
var PsyncStage = map[int32]string{
	0:         "psync-init",
	1:      "psync-retrieve-rdb",
	2:  "psync-receive-rdb-done",
	3:      "psync-send-rdb",
	4: "psync-parse-rdb-done",
	5:      "psync-send-aof",
}
```

writer 状态取值如下，
```
var StageCode = map[int32]string{
	0:    "init",
	1: "send-rdb",
    2: "send-rdb-done",
	3: "send-aof",
}
```


- Set log level dynamically.

```
POST /api/v1/tasks

{
    "errno":200,
    "errmsg":"ok",
    "data":"set log level info"
}

usuage:  curl -X POST -d level=debug http://127.0.0.1:4000/api/v1/log
support level: debug/info/warn/error/panic/fatal
```

- Get config in running server,
```
GET /api/v1/cfg

{
	"errno": 200,
	"errmsg": "ok",
	"data": {
        "ver": "1.0.4",
		"dial_timeout_sec": 30,
		"read_timeout_sec": 20,
		"write_timeout_sec": 10,
		"rdb_restore_command_behavior": "rewrite",
		"pipeline_write_batch": 1000,
		"pipeline_write_delay_msec": 1000,
		"syncer": {
			"hnb-v.src#hna-v.dst#1": {
				"write_ignore_err": false
			}
		}
	}
}
```
also, we can change some configures,
```
POST /api/v1/cfg
{
    "whitelist": [
        "127.0.0.1"
    ],
    "origin_cmd_enabled"： "no", // 不同步非 __ 前缀的命令，收到后会打印错误日志
    "pipeline_write_batch": 100,
    "pipeline_write_delay_msec": 1000,
    "syncer": {
        "hnb-v.src#hna-v.dst#1": {
            "write_ignore_err": true
        }
    }
}
```
**注意**：为了简化判断逻辑，使用 `alert_enabled_new` 替换原来的 `alert_enabled`，取值为 "yes" 和 "no"，如果不修改该配置可以置空。

- Do some filter
```
POST /api/v1/filter
{
    "file_name": "xxxxxxx"
}
```
also, we can delete it.
```
DELETE /api/v1/filter
```

- Get slots cached inside go-redis
```
GET /api/v1/slots

{
    "errno": 200,
    "errmsg": "ok",
    "data": {
        "hnb-v.aiot#hna-v.aiot#1": [
            "0-5460 Redis<************:9000 db:0> Redis<************:9004 db:0>",
            "5461-10922 Redis<************:9001 db:0> Redis<************:9005 db:0>",
            "10923-16383 Redis<************:9003 db:0> Redis<************:9002 db:0>"
        ]
    }
}
```

- Get modules supported
```
GET /api/v1/modules
{
    "errno": 200,
    "errmsg": "ok",
    "data": [
        {
            "name":"tairzset_",
            "ver":1
        }
    ]
}
```

## Monitor supported 

see file `internal/metrics/prom.go`