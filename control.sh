#! /bin/bash

module="fusion-syncer"

pidfile=${module}.pid
confile=config/${module}.toml

function die() {
    echo "Error: $1"
    exit 1
}

function usage() {
	echo -ne "usage:\n\tsh $0 <action>\n"
	echo -ne "Options and arguments:\n"
	echo -ne "\taction : [start|stop]\n"
}

function start() {
    nohup bin/$module $confile >> log/syncer.out 2>&1 &

    if [ $? -eq 0 ]; then
        echo "start $module ok, pid=$!"
        echo $! > "$pidfile"
    else
        echo "start $module fail, status:$?"
    fi
}

function stop() {
    kill -9 "$(get_pid)" &> /dev/null
    echo "stop $module, status:$?"
}

function get_pid() {
    if [ -f $pidfile ];then
        cat $pidfile
    fi
}

function restart() {
    stop 
    start 
}

case $1 in
    "start" )
        start ;;
    "stop" )
        stop ;;
    "restart" )
        restart ;;
    * )
        usage ;;
esac