#! /bin/bash

set -e
#set -x
workspace=$(cd "$(dirname "$0")" && pwd -P)

# Check Go version and use Go 1.24.4 if current version <= 1.23
check_go_version() {
    local current_version
    current_version=$(go version 2>/dev/null | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//' || echo "0.0")
    
    if [ -n "$current_version" ]; then
        # Convert version to comparable format (e.g., 1.23 -> 123, 1.24 -> 124)
        local major minor
        major=$(echo "$current_version" | cut -d. -f1)
        minor=$(echo "$current_version" | cut -d. -f2)
        local version_num=$((major * 100 + minor))
        
        # If version <= 1.23 (123), use Go 1.24.4
        if [ "$version_num" -le 123 ]; then
            echo "Current Go version is $current_version (<= 1.23), switching to Go 1.24.4"
            export GOROOT=/usr/local/go1.24.4
            export PATH=$GOROOT/bin:$PATH
        else
            echo "Current Go version is $current_version (> 1.23), using current version"
        fi
    else
        echo "Go not found, using Go 1.24.4"
        export GOROOT=/usr/local/go1.24.4
        export PATH=$GOROOT/bin:$PATH
    fi
}

# Check and set Go version
check_go_version

export GOPROXY=http://goproxy.intra.xiaojukeji.com,direct
export GO111MODULE=auto
# export GOSUMDB=off
export CGO_CFLAGS="-I$PWD/third-party/rocksdb/include"
export CGO_LDFLAGS="-L$PWD/third-party/rocksdb/ -lbz2 -llz4 -lsnappy -lz -lzstd -lrocksdb"
module="fusion-syncer"

#print go version
go version

cd "${workspace}" && make 
ret=$?
if [ $ret -ne 0 ];then
    echo "===== $module build failed ====="
    exit $ret
else
    echo "===== $module build success ====="
fi

