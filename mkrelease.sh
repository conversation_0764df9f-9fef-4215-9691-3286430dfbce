#! /bin/bash

version_file="internal/server/version.go"
test -f "$version_file"|| touch "$version_file"
version=$(grep Version "$version_file")

make_version() {
    git_last_time=$(git log | grep 'Date:' | head -1 | cut -c9-)
    git_sha1=$(git log | grep 'commit' | head -1 | cut -c8-15)
    git_dirty=$(git diff --no-ext-diff 2> /dev/null | wc -l)
    build_id=$(uname -n)"-"$(date)

    cat << EOF | gofmt > "$version_file"
    package server
    const (
        $version
        GitLastTime = "$git_last_time"
        GitSha1 = "$git_sha1"
        GitDirty = $git_dirty
        BuildID = "$build_id"
    )
EOF
}

reset_version() {
    cat << EOF | gofmt > "$version_file"
    package server
    const (
        $version
        GitLastTime = ""
        GitSha1 = ""
        GitDirty = 0
        BuildID = ""
    )
EOF
}

if [ "$1" == "0" ]; then
    make_version
else
    reset_version
fi






