package main

import (
	"context"
	"os"
	"path"

	r2config "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_config"
	r2mgr "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_mgr"
	r2server "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_server"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/server"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
)

func main() {
	if len(os.Args) != 2 {
		println("Usage: fusion-syncer <config file>")
		println(server.PrintVersion())
		os.Exit(1)
	}

	configFile := os.Args[1]
	config, err := r2config.LoadConfig(configFile)
	if err != nil {
		panic("Error loading config: " + err.Error())
	}

	logger.Init(config.General.LogDir, config.General.LogLevel)
	logger.Infof("Fusion Syncer starting with config file: %s", configFile)
	logger.Infof("Config: %+v", config)

	rateLimitConfigPath := path.Join(config.General.Dir, "ratelimit.toml")
	rateLimitMgr, err := r2mgr.NewRateLimitConfigMgr(rateLimitConfigPath)

	if err != nil {
		logger.Errorf("Error initializing rate limit manager: %s", err.Error())
		os.Exit(1)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	memLimiter := r2mgr.NewMemMgrLimiter(config.General.MemLimitMiB << 20)
	syncMgr, err := r2mgr.NewR2SyncManager(ctx, config, rateLimitMgr, memLimiter)
	if err != nil {
		logger.Errorf("Error initializing sync manager: %s", err.Error())
		os.Exit(1)
	}

	srv, err := r2server.NewServer(config, syncMgr, rateLimitMgr, memLimiter)
	if err != nil {
		logger.Errorf("Error initializing server:", err.Error())
		os.Exit(1)
	}
	srv.StartAPIServer()
	logger.Infof("Fusion Syncer started successfully.")
	defer func() {
		if err := srv.Stop(); err != nil {
			logger.Errorf("Error stopping server: %v", err)
		} else {
			logger.Infof("Fusion Syncer stopped successfully.")
		}
	}()
	select {} // Block forever
}
