ROOT_DIR := $(abspath $(dir $(lastword $(MAKEFILE_LIST))))
ZIG_TAR := $(ROOT_DIR)/third-party/zig-x86_64-linux-0.14.1.tar.xz
ZIG_DIR := $(ROOT_DIR)/third-party/zig-x86_64-linux-0.14.1
ZIG_BIN := $(ZIG_DIR)/zig
ZIG_TARGET := x86_64-linux-gnu.2.17
PKG := git.xiaojukeji.com/fusion/fusion-syncer

# Check Go version and set Go 1.24.4 if needed
GO_VERSION_CHECK := $(shell \
	current_version=$$(go version 2>/dev/null | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//' || echo "0.0"); \
	if [ -n "$$current_version" ]; then \
		major=$$(echo "$$current_version" | cut -d. -f1); \
		minor=$$(echo "$$current_version" | cut -d. -f2); \
		version_num=$$((major * 100 + minor)); \
		if [ "$$version_num" -le 123 ]; then \
			echo "SWITCH_GO"; \
		fi; \
	else \
		echo "SWITCH_GO"; \
	fi)

ifeq ($(GO_VERSION_CHECK),SWITCH_GO)
export GOROOT := /usr/local/go1.24.4
export PATH := $(GOROOT)/bin:$(PATH)
$(info Switching to Go 1.24.4 due to version <= 1.23)
endif

export GOPROXY=http://goproxy.intra.xiaojukeji.com,direct
export GO111MODULE=auto
export CGO_CFLAGS="-I$(ROOT_DIR)/third-party/rocksdb/include"
export CGO_LDFLAGS=-L$(ROOT_DIR)/third-party/rocksdb/ -lbz2 -llz4 -lsnappy -lz -lzstd -lrocksdb

ZIG_CC := $(ZIG_BIN) cc -target $(ZIG_TARGET)
ZIG_CXX := $(ZIG_BIN) c++ -target $(ZIG_TARGET)

TARGET_NAME := fusion-syncer
OUTPUT_DIR := output
SYNCER_BIN := $(OUTPUT_DIR)/$(TARGET_NAME)
VERSION := 1.0.0


.PHONY: all clean deployment

all: deployment

deployment: $(SYNCER_BIN)
	@echo "Preparing deployment assets for ${TARGET_NAME}..."
	mkdir -p ${OUTPUT_DIR}
	mkdir -p ${OUTPUT_DIR}/logs
	cp -f control.sh ${OUTPUT_DIR}/
	chmod +x ${OUTPUT_DIR}/control.sh
	cp -rf config ${OUTPUT_DIR}/
	@echo "Deployment assets ready in ${OUTPUT_DIR}/"

$(SYNCER_BIN): $(ZIG_BIN)
	@echo "Making version file for ${TARGET_NAME}"
	$(eval BUILD_TIME := $(shell date -u +'%Y-%m-%dT%H:%M:%SZ'))
	$(eval BUILD_ID := $(shell echo "$(shell hostname)-$(shell date +%y%m%d-%H%M%S)" | sed 's/ /_/g'))
	$(eval GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "nogit"))
	$(eval GIT_DIRTY := $(shell git diff --quiet 2>/dev/null || echo "-dirty"))
	$(eval GO_VERSION := $(shell go version | awk '{print $$3}'))
	$(eval PLATFORM := $(shell go env GOOS)/$(shell go env GOARCH))
	@echo "Building ${TARGET_NAME}..."
	@echo "GIT_COMMIT: $(GIT_COMMIT)"
	CC="$(ZIG_CC)" CXX="$(ZIG_CXX)" \
		go build -mod=mod -v \
		-ldflags "\
			-X '$(PKG)/internal/server.Version=$(VERSION)' \
			-X '$(PKG)/internal/server.GitCommit=$(GIT_COMMIT)' \
			-X '$(PKG)/internal/server.BuildTime=$(BUILD_TIME)' \
			-X '$(PKG)/internal/server.BuildID=$(BUILD_ID)' \
			-X '$(PKG)/internal/server.GoVersion=$(GO_VERSION)' \
			-X '$(PKG)/internal/server.Platform=$(PLATFORM)' \
			-X '$(PKG)/internal/server.GitDirty=$(GIT_DIRTY)'" \
		-o "${OUTPUT_DIR}/${TARGET_NAME}" "./cmd/${TARGET_NAME}"

$(ZIG_BIN): $(ZIG_TAR)
	@echo "Extracting Zig compiler..."
	mkdir -p $(ZIG_DIR)
	tar -xf $< -C $(ZIG_DIR) --strip-components=1
	@touch -r $< $(ZIG_BIN) # update timestamp to avoid re-extraction
	@echo "Zig compiler ready at $(ZIG_BIN)"

clean:
	rm -rf ${OUTPUT_DIR}

