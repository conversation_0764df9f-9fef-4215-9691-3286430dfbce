package pipe

import (
	"fmt"
	"os"
)

const (
	defaultMemSize = (1 << (10 * 2)) * 64 // 64MB
	writePerSize   = (1 << (10 * 2)) * 8  // 64MB
)

type memFileBuffer struct {
	mb          *memBuffer
	fb          *fileBuffer
	writeToFile bool
}

func newMemFileBufferDefault(fileSize int, f *os.File) *memFileBuffer {
	return &memFileBuffer{
		mb:          newMemBuffer(defaultMemSize),
		fb:          newFileBuffer(fileSize, f),
		writeToFile: false,
	}
}

func newMemFileBuffer(buffSize int, fileSize int, f *os.File) *memFileBuffer {
	return &memFileBuffer{
		mb:          newMemBuffer(buffSize),
		fb:          newFileBuffer(fileSize, f),
		writeToFile: false,
	}
}

func (p *memFileBuffer) readSome(b []byte) (int, error) {
	if p.writeToFile {
		// file -> mem
		block := make([]byte, writePerSize)
		for {
			av := p.mb.available()
			if av == 0 {
				break
			} else if av < writePerSize {
				block = block[:av]
			}
			n, err := p.fb.readSome(block)
			p.mb.writeSome(block[:n])
			if n < writePerSize || err != nil {
				if err != nil {
					fmt.Printf("memFileBuffer read file err: %v\n", err)
				}
				break
			}
		}
		if p.fb.buffered() == 0 {
			p.writeToFile = false
			fmt.Printf("change write source, write to mem. mem bufferd:%d\n",
				p.mb.buffered())
		}
	}
	return p.mb.readSome(b)
}

func (p *memFileBuffer) writeSome(b []byte) (int, error) {
	if !p.writeToFile && len(b) > p.mb.available() {
		p.writeToFile = true
		fmt.Printf("change write source, write to file. mem bufferd:%d, remain:%d < want:%d\n",
			p.mb.buffered(), p.mb.available(), len(b))
	}
	if p.writeToFile {
		return p.fb.writeSome(b)
	}
	return p.mb.writeSome(b)
}

func (p *memFileBuffer) buffered() int {
	return p.mb.buffered() + p.fb.buffered()
}

func (p *memFileBuffer) available() int {
	return p.mb.available() + p.fb.available()
}

func (p *memFileBuffer) rclose() error {
	p.mb.rclose()
	return p.fb.rclose()
}

func (p *memFileBuffer) wclose() error {
	p.mb.wclose()
	return p.fb.wclose()
}
