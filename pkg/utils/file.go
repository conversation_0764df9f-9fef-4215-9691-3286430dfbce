package utils

import (
	"os"
)

func DoesFileExist(fileName string) (bool, error) {
	_, err := os.Stat(fileName)
	if err != nil {
		return false, err
	}
	return true, nil
}

func OpenReadFile(name string) (*os.File, int64, error) {
	f, err := os.Open(name)
	if err != nil {
		return nil, 0, err
	}
	s, err := f.Stat()
	if err != nil {
		return nil, 0, err
	}
	return f, s.<PERSON>(), nil
}

func OpenWriteFile(name string) (*os.File, error) {
	return os.OpenFile(name, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0666)
}

func OpenReadWriteFile(name string) (*os.File, error) {
	return os.OpenFile(name, os.O_CREATE|os.O_RDWR|os.O_TRUNC, 0666)
}

func WriteFileAtomic(name string, data []byte) error {
	tmpFile := name + ".tmp"
	f, err := OpenWriteFile(tmpFile)
	if err != nil {
		return err
	}
	defer f.Close()

	if _, err := f.Write(data); err != nil {
		return err
	}

	if err := f.Close(); err != nil {
		return err
	}

	if err := os.Rename(tmpFile, name); err != nil {
		return err
	}

	return nil
}
