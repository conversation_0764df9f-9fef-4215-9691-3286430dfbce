package utils

import (
	"fmt"
	"strings"
)

var byteToEscape = map[byte]string{
	'\a': `\a`, // 0x07
	'\b': `\b`, // 0x08
	'\t': `\t`, // 0x09
	'\n': `\n`, // 0x0a
	'\v': `\v`, // 0x0b
	'\f': `\f`, // 0x0c
	'\r': `\r`, // 0x0d
	'\\': `\\`, // 0x5c
}

func EscapeBytes(data []byte) string {
	var builder strings.Builder
	for _, b := range data {
		if esc, ok := byteToEscape[b]; ok {
			builder.WriteString(esc)
			continue
		}

		if b >= 0x20 && b <= 0x7e {
			builder.WriteByte(b)
			continue
		}

		builder.WriteString(fmt.Sprintf("\\x%02x", b))
	}
	return builder.String()
}
