package utils

import (
	"cmp"
	"context"
	"fmt"
	"net"
	"runtime"
	"strings"
	"sync"
	"time"

	"maps"

	"golang.org/x/exp/constraints"
)

const (
	_ = 1 << (10 * iota)
	KB
	MB
	GB
	TB
	PB
)

func GetLocalIP() string {
	var ip string
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ip
	}
	for _, addr := range addrs {
		ipnet, ok := addr.(*net.IPNet)
		if !ok || ipnet.IP.IsLoopback() {
			continue
		}
		if ipnet.IP.To4() != nil {
			ip = ipnet.IP.String()
			break
		}
	}
	return ip
}

func Sleep(ctx context.Context, dur time.Duration) error {
	t := time.NewTimer(dur)
	defer t.Stop()

	select {
	case <-t.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

func IsCanceled(ctx context.Context) error {
	return ctx.Err()
}

func Errorf(format string, args ...any) error {
	_, file, line, _ := runtime.Caller(1)
	baseErr := fmt.Errorf(format, args...)
	return fmt.Errorf("[%s:%d] %w", file, line, baseErr)
}

func CopyMap[K comparable, V any](src map[K]V) map[K]V {
	if src == nil {
		return nil
	}
	dst := make(map[K]V, len(src))
	maps.Copy(dst, src)
	return dst
}

type Map[K comparable, V any] struct {
	m sync.Map
}

func (m *Map[K, V]) Delete(key K) { m.m.Delete(key) }
func (m *Map[K, V]) Load(key K) (value V, ok bool) {
	v, ok := m.m.Load(key)
	if !ok {
		return value, ok
	}
	return v.(V), ok
}
func (m *Map[K, V]) LoadAndDelete(key K) (value V, loaded bool) {
	v, loaded := m.m.LoadAndDelete(key)
	if !loaded {
		return value, loaded
	}
	return v.(V), loaded
}

// LoadOrStore returns the existing value for the key if present.
// Otherwise, it stores and returns the given value.
// The loaded result is true if the value was loaded, false if stored.
func (m *Map[K, V]) LoadOrStore(key K, value V) (actual V, loaded bool) {
	a, loaded := m.m.LoadOrStore(key, value)
	return a.(V), loaded
}
func (m *Map[K, V]) Range(f func(key K, value V) bool) {
	m.m.Range(func(key, value any) bool { return f(key.(K), value.(V)) })
}
func (m *Map[K, V]) Store(key K, value V) { m.m.Store(key, value) }
func (m *Map[K, V]) CompareAndDelete(key K, value V) (deleted bool) {
	return m.m.CompareAndDelete(key, value)
}
func (m *Map[K, V]) CompareAndSwap(key K, old, new V) (swapped bool) {
	return m.m.CompareAndSwap(key, old, new)
}

func CopySlice[T any](src []T) []T {
	if src == nil {
		return nil
	}

	dst := make([]T, len(src))
	copy(dst, src)
	return dst
}

func SendWithContext[T any](ctx context.Context, ch chan<- T, value T) error {
	select {
	case ch <- value:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

func LastIndexOf[T cmp.Ordered](slice []T, value T) int {
	if len(slice) == 0 {
		return -1
	}

	for i := len(slice) - 1; i >= 0; i-- {
		if slice[i] == value {
			return i
		}
	}

	return -1
}

// IteratorFunc: Fn() -> Optional<T>
type IteratorFunc[T any] func() (T, bool)

func Chunk[T any](iter IteratorFunc[T], batchSize int) IteratorFunc[[]T] {
	return func() ([]T, bool) {
		chunk := make([]T, 0, batchSize)
		for range batchSize {
			item, ok := iter()
			if !ok {
				if len(chunk) == 0 {
					return nil, false // No more items
				}
				return chunk, true // Return the chunk if it's not empty
			}
			chunk = append(chunk, item)
		}
		return chunk, true // Return the full chunk
	}
}

func Filter[T any](items []T, predicate func(T) bool) []T {
	var result []T
	for _, item := range items {
		if predicate(item) {
			result = append(result, item)
		}
	}
	return result
}

func Partition[T any](items []T, predicate func(T) bool) (matching []T, nonMatching []T) {
	for _, item := range items {
		if predicate(item) {
			matching = append(matching, item)
		} else {
			nonMatching = append(nonMatching, item)
		}
	}
	return
}

func FilterLazy[T any](iter IteratorFunc[T], predicate func(T) bool) IteratorFunc[T] {
	return func() (T, bool) {
		for {
			item, ok := iter()
			if !ok {
				return item, false // No more items
			}
			if predicate(item) {
				return item, true // Return the item if it matches the predicate
			}
		}
	}
}

func ToLazyIterator[T any](items []T) IteratorFunc[T] {
	index := 0
	return func() (T, bool) {
		if index >= len(items) {
			var zero T
			return zero, false // No more items
		}
		item := items[index]
		index++
		return item, true // Return the next item
	}
}

func SplitLazy(s string, sep string) IteratorFunc[string] {
	if sep == "" {
		index := 0
		return func() (string, bool) {
			if index >= len(s) {
				return "", false // No more items
			}
			item := string(s[index])
			index++
			return item, true // Return the next character
		}
	}

	curStr := s
	var closure func() (string, bool)

	closure = func() (string, bool) {
		if curStr == "" {
			return "", false
		}

		idx := strings.Index(curStr, sep)
		if idx < 0 {
			curStr = ""
			return curStr, true // Return the last part
		}

		if idx == 0 {
			curStr = curStr[len(sep):]
			return closure()
		}

		result := curStr[:idx]
		curStr = curStr[idx+len(sep):] // Move past the separator
		return result, true            // Return the part before the separator
	}

	return closure
}

func Abs[T constraints.Integer | constraints.Float](value T) T {
	return max(value, -value)
}

func AbsDiff[T constraints.Unsigned](a, b T) T {
	if a < b {
		return b - a
	}
	return a - b
}

type Guarded[T any] struct {
	mu    sync.Mutex
	value T
}

func NewGuarded[T any](value T) *Guarded[T] {
	return &Guarded[T]{value: value}
}

func (g *Guarded[T]) Unlock() (*T, func()) {
	g.mu.Lock()
	return &g.value, func() {
		g.mu.Unlock()
	}
}

func MakeAddr(host string, port int) string {
	return fmt.Sprintf("%s:%d", host, port)
}
