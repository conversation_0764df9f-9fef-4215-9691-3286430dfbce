package db

import (
	"testing"
)

const (
	addr     = "************:7005"
	username = ""
	password = "123456"
)

func TestRedisConfigGet(t *testing.T) {
	conn := GetDefaultRedisClient(addr, username, password)
	defer conn.Close()

	v, err := RedisConfigGet(conn, username, password, "aof-psync-enabled")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Logf("val = %v", v)
}

func TestRedisRole(t *testing.T) {
	master, slaves, err := RedisRole(addr, username, password)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("master: %v", master)
	t.Logf("slaves: %v", slaves)
}

func Test_parseRedisInfoSectionItem(t *testing.T) {
	item := "ip=************,port=6004,state=online,offset=600583,lag=0,opid=20"
	item = ""
	t.Log(parseRedisInfoSectionItem(item))
}

func TestRedisInfo(t *testing.T) {
	t.Log("execute cmd by addr")
	res, err := RedisInfo(addr, username, password, "server")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)

	t.Log("execute cmd by conn")
	conn := GetDefaultRedisClient(addr, username, password)
	defer conn.Close()

	res, err = RedisInfo(conn, username, password, "server")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestGetRedisShardInfo(t *testing.T) {
	res, err := GetRedisShardInfo(addr, username, password, 2)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestGetRedisSyncer(t *testing.T) {
	res, err := GetRedisSyncer(addr, username, password, "hna-v.dst")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestGetRedisClusterNodesAddrs(t *testing.T) {
	t.Log(GetRedisClusterNodesAddrs(username, password, addr))
}

func TestGetRedisClusterInfoMap(t *testing.T) {
	infoMap, err := GetRedisClusterInfoMap(addr, username, password)
	if err != nil {
		t.Logf("get cluster info via addr %s failed:%v", addr, err)
	}
	t.Logf("%v", infoMap)
}

func TestCheckClusterInfoOk(t *testing.T) {
	/** 通过检查cluster info检查节点视角集群是否正常，避免以不正常的节点拉路由创建writer
	 * case1: 集群的cluster_state=ok && cluster_slots_assigned=16384, 能够正常检查通过
	 * case2: 构造slot缺失, 通过cluster delslot删除一个slot, 此时cluster_slots_assigned=16383, 检查不通过
	 * */
	infoMap, err := GetRedisClusterInfoMap(addr, "", "")
	if err != nil {
		t.Fatalf("get cluster info via addr %s failed:%v", addr, err)
	}
	if !CheckClusterInfoOk(infoMap) {
		t.Fatalf("check cluster info via addr %s failed, now cluster info: %v", addr, infoMap)
	}
	t.Logf("check cluster info via addr %s ok, now cluster info: %v", addr, infoMap)
}
