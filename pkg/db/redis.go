package db

import (
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"git.xiaojukeji.com/kvdb/go-redis/v8"
	"github.com/pkg/errors"
)

const (
	CRLF = "\r\n"
)

func GetRedisClient(redisAddr, username, password string, dialTimeout, readTimeout, writeTimeout time.Duration) *redis.Client {
	c := redis.NewClient(&redis.Options{
		Addr:         redisAddr,
		DialTimeout:  dialTimeout,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
		MaxRetries:   2,
		PoolSize:     1,
		Username:     username,
		Password:     password,
		// PoolTimeout:        30 * time.Second,
		// IdleTimeout:        time.Minute,
		// IdleCheckFrequency: 100 * time.Millisecond,
	})
	return c
}

func GetDefaultRedisClient(redisAddr, username, password string) *redis.Client {
	dialTimeout := 2 * time.Second
	readTimeout := time.Second
	writeTimeout := time.Second
	return GetRedisClient(redisAddr, username, password, dialTimeout, readTimeout, writeTimeout)
}

func RedisInfo(c any, username, password, section string) (map[string]string, error) {
	if c == nil {
		return nil, errors.Errorf("conn is nil when info %s", section)
	}
	conn, ok := c.(*redis.Client)
	if !ok {
		addr := c.(string)
		conn = GetDefaultRedisClient(addr, username, password)
		defer conn.Close()
	}

	// *redis.Client,
	info := make(map[string]string)
	res, err := conn.Info(conn.Context(), section).Result()
	if err != nil {
		return info, err
	}
	res = strings.TrimSpace(res)
	items := strings.Split(res, CRLF)
	for _, item := range items {
		kv := strings.Split(item, ":")
		if len(kv) != 2 {
			continue
		}
		info[kv[0]] = kv[1]
	}
	return info, nil
}

func RedisConfigGet(c any, username, password string, field string) (string, error) {
	if c == nil {
		return "", errors.Errorf("Conn is nil when config get %s", field)
	}
	conn, ok := c.(*redis.Client)
	if !ok {
		addr := c.(string)
		conn = GetDefaultRedisClient(addr, username, password)
		defer conn.Close()
	}

	val, err := conn.ConfigGet(conn.Context(), field).Result()
	if err != nil {
		return "", err
	}
	if len(val) == 0 {
		return "", fmt.Errorf("unknown config %s", field)
	}
	return val[1].(string), nil
}

// e.g. ip=************,port=6004,state=online,offset=600583,lag=0,opid=20
func parseRedisInfoSectionItem(item string) map[string]string {
	res := make(map[string]string)
	for _, b := range strings.Split(item, ",") {
		kv := strings.Split(b, "=")
		if len(kv) != 2 {
			continue
		}
		res[kv[0]] = kv[1]
	}
	return res
}

// GetRedisShardInfo return shared roles, message get from master,
// i.e. ************:7000 master
//
//	************:7003 slave0
func GetRedisShardInfo(addr, username, password string, retries int) (map[string]string, error) {
	info := make(map[string]string)

	res, err := RedisInfo(addr, username, password, "Replication")
	if err != nil {
		return info, err
	}
	if res["role"] == "master" {
		info[addr] = "master"
		slaven, _ := strconv.Atoi(res["connected_slaves"])
		for i := 0; i < slaven; i++ {
			index := fmt.Sprintf("slave%d", i)
			slaveInfo := parseRedisInfoSectionItem(res[index])
			if slaveInfo["state"] != "online" {
				continue
			}
			info[net.JoinHostPort(slaveInfo["ip"], slaveInfo["port"])] = index
		}
	} else if res["role"] == "slave" {
		if res["master_link_status"] == "up" {
			master := res["master_host"] + ":" + res["master_port"]
			return GetRedisShardInfo(master, username, password, retries)
		}
		retries--
		if retries == 0 {
			return info, fmt.Errorf("%s cannot turn into master", addr)
		}
		time.Sleep(2 * time.Second)
		return GetRedisShardInfo(addr, username, password, retries)
	} else {
		return info, fmt.Errorf("unknown role %s of %s", res["role"], addr)
	}
	return info, nil
}

func GetRedisClusterNodes(addr, username, passwd string) (string, error) {
	conn := GetDefaultRedisClient(addr, username, passwd)
	defer conn.Close()
	return conn.ClusterNodes(conn.Context()).Result()
}

func GetRedisClusterNodesAddrs(addr, username, passwd string) ([]string, error) {
	nodes, err := GetRedisClusterNodes(addr, username, passwd)
	if err != nil {
		return nil, err
	}
	var addrs []string
	for _, line := range strings.Split(nodes, "\n") {
		items := strings.Split(line, " ")
		if len(items) < 2 {
			continue
		}
		addr := strings.Split(items[1], "@")[0]
		addrs = append(addrs, addr)
	}
	return addrs, nil
}

func GetRedisClusterInfo(addr, username, passwd string) (string, error) {
	conn := GetDefaultRedisClient(addr, username, passwd)
	defer conn.Close()
	return conn.ClusterInfo(conn.Context()).Result()
}

func GetRedisClusterInfoMap(addr, username, passwd string) (map[string]string, error) {
	info, err := GetRedisClusterInfo(addr, username, passwd)
	if err != nil {
		return nil, err
	}
	clusterInfo := make(map[string]string)
	for _, line := range strings.Split(info, "\r\n") {
		items := strings.Split(line, ":")
		if len(items) < 2 {
			continue
		}
		clusterInfo[items[0]] = items[1]
	}
	return clusterInfo, nil
}

func CheckClusterInfoOk(clusterinfo map[string]string) bool {
	return clusterinfo["cluster_state"] == "ok" &&
		clusterinfo["cluster_slots_assigned"] == "16384"
}

// GetRedisSyncer return format "<replid> <offset> <opid> <shard_id>"
func GetRedisSyncer(addr, username, password, dstCluster string) ([]any, error) {
	c := GetDefaultRedisClient(addr, username, password)
	defer c.Close()

	return c.Do(c.Context(), "replconf", "getsyncer", dstCluster).Slice()
}

func RedisRole(addr, username, password string) (master string, slaves map[string]bool, err error) {
	c := GetDefaultRedisClient(addr, username, password)
	defer c.Close()

	slaves = make(map[string]bool)
	res, err := c.Do(c.Context(), "role").Slice()
	if err != nil {
		return
	}

	if res[0].(string) == "master" {
		master = addr
		if len(res) < 3 {
			return
		}
		myslaves, ok := res[2].([]any)
		if !ok {
			err = fmt.Errorf("parse slaves failed, %v", res[2])
			return
		}
		for _, slave := range myslaves {
			slaveInfo, ok := slave.([]any)
			if !ok || len(slaveInfo) < 3 {
				err = fmt.Errorf("parse slaveinfo failed, %v", slave)
				return
			}
			ipport := fmt.Sprintf("%v:%v", slaveInfo[0], slaveInfo[1])
			slaves[ipport] = true
		}
		return
	}
	slaves[addr] = true
	master = fmt.Sprintf("%v:%v", res[1], res[2])
	return
}

func RedisGetShardAddrs(addr, username, password string) (master string, slaves []string, err error) {
	var info map[string]string
	info, err = GetRedisShardInfo(addr, username, password, 15) // wait 30s at most（ 2 * cluster-timeout ）
	if err != nil {
		return
	}
	slaves = []string{}

	for ipport, role := range info {
		if role == "master" {
			master = ipport
			continue
		}
		slaves = append(slaves, ipport)
	}
	return
}
