package logger

// ref: https://www.liwenzhou.com/posts/Go/zap/
// ref: https://www.yisu.com/zixun/154695.html

import (
	"fmt"
	"io"
	"log"
	"os"
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var atomLevel zap.AtomicLevel
var zapLogger *zap.SugaredLogger

func Init(logPath, level string) {
	if logPath == "" {
		log.Fatalf("logPath is empty, please set it in config file")
	}

	if err := os.MkdirAll(logPath, os.ModePerm); err != nil {
		log.Fatalf("failed to create log directory: %v", err)
	}

	if level == "" {
		level = "debug"
	}
	encoder := zapcore.NewConsoleEncoder(zapcore.EncoderConfig{
		TimeKey:     "ts",
		MessageKey:  "msg",
		LevelKey:    "level",
		CallerKey:   "file",
		LineEnding:  zapcore.DefaultLineEnding,
		EncodeLevel: zapcore.CapitalLevelEncoder,
		EncodeTime: func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
			enc.AppendString(t.Format("2006-01-02 15:04:05"))
		},
		EncodeCaller:   zapcore.ShortCallerEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
	})

	atomLevel = zap.NewAtomicLevel()
	atomLevel.SetLevel(parseLogLevel(level))
	infoLevel := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		return lvl <= zapcore.WarnLevel && lvl >= atomLevel.Level()
	})
	errorLevel := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		return lvl >= zapcore.ErrorLevel
	})

	infoWriter := getWriter(logPath)
	// errorWriter := getWriter(errPath)
	errorWriter := infoWriter
	core := zapcore.NewTee(
		zapcore.NewCore(encoder, zapcore.AddSync(infoWriter), infoLevel),
		zapcore.NewCore(encoder, zapcore.AddSync(errorWriter), errorLevel),
	)

	log := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	zapLogger = log.Sugar()
}

func getWriter(logDir string) io.Writer {
	logNamePattern := fmt.Sprintf("%s/%%Y%%m%%d.log", logDir)

	hook, err := rotatelogs.New(
		logNamePattern,
		rotatelogs.WithRotationTime(24*time.Hour),
		rotatelogs.WithMaxAge(14*24*time.Hour),
		rotatelogs.WithLinkName(logDir+"/latest.log"),
	)
	if err != nil {
		log.Fatal(err)
	}
	return hook
}

func parseLogLevel(level string) zapcore.Level {
	var logLevel zapcore.Level

	switch level {
	case "debug":
		logLevel = zap.DebugLevel
	case "info":
		logLevel = zap.InfoLevel
	case "warn":
		logLevel = zap.WarnLevel
	case "error":
		logLevel = zap.ErrorLevel
	case "panic":
		logLevel = zap.PanicLevel
	case "fatal":
		logLevel = zap.FatalLevel
	default:
		logLevel = zap.InfoLevel
	}
	return logLevel
}

func IsDebugMode() bool {
	return atomLevel.Level() <= zap.DebugLevel
}

func SetLoglevel(level string) {
	logLevel := parseLogLevel(level)
	atomLevel.SetLevel(logLevel)
}

func Logger() *zap.SugaredLogger {
	return zapLogger
}

func Debug(args ...any) {
	zapLogger.Debug(args...)
}

func Debugf(template string, args ...any) {
	zapLogger.Debugf(template, args...)
}

func Info(args ...any) {
	zapLogger.Info(args...)
}

func Infof(template string, args ...any) {
	zapLogger.Infof(template, args...)
}

func Warn(args ...any) {
	zapLogger.Warn(args...)
}

func Warnf(template string, args ...any) {
	zapLogger.Warnf(template, args...)
}

func Error(args ...any) {
	zapLogger.Error(args...)
}

func Errorf(template string, args ...any) {
	zapLogger.Errorf(template, args...)
}

func DPanic(args ...any) {
	zapLogger.DPanic(args...)
}

func DPanicf(template string, args ...any) {
	zapLogger.DPanicf(template, args...)
}

func Panic(args ...any) {
	zapLogger.Panic(args...)
}

func Panicf(template string, args ...any) {
	zapLogger.Panicf(template, args...)
}

func PanicError(args ...any) {
	zapLogger.Panic(args...)
}

func Fatal(args ...any) {
	zapLogger.Fatal(args...)
}

func Fatalf(template string, args ...any) {
	zapLogger.Fatalf(template, args...)
}

func GetLogger() *zap.Logger {
	return zapLogger.Desugar()
}
