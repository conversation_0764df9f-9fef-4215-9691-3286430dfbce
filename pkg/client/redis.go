package client

import (
	"bufio"
	"crypto/tls"
	"net"
	"sync/atomic"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/client/proto"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"

	"github.com/pkg/errors"
)

type Redis struct {
	conn         net.Conn
	reader       *bufio.Reader
	readBytes    int64 // read bytes use reader
	writer       *bufio.Writer
	protoReader  *proto.Reader // 解析 RESP
	protoWriter  *proto.Writer
	writeTimeout time.Duration
	readTimeout  time.Duration
	useMemFile   bool
}

type dialOptions struct {
	readTimeout  time.Duration
	writeTimeout time.Duration
	dialer       *net.Dialer
	useTLS       bool
	username     string
	password     string
}

type DialOption struct {
	f func(*dialOptions)
}

func ReadTimeoutDuration(d time.Duration) DialOption {
	return DialOption{func(do *dialOptions) {
		do.readTimeout = d
	}}
}

func DialReadTimeout(d int) DialOption {
	return DialOption{func(do *dialOptions) {
		do.readTimeout = time.Duration(d) * time.Second
	}}
}

func DialWriteTimeout(d int) DialOption {
	return DialOption{func(do *dialOptions) {
		do.writeTimeout = time.Duration(d) * time.Second
	}}
}

func DialConnectTimeoutDuration(d time.Duration) DialOption {
	return DialOption{func(do *dialOptions) {
		do.dialer.Timeout = d
	}}
}

func DialConnectTimeout(d int) DialOption {
	return DialOption{func(do *dialOptions) {
		do.dialer.Timeout = time.Duration(d) * time.Second
	}}
}

func DialUsername(username string) DialOption {
	return DialOption{func(do *dialOptions) {
		do.username = username
	}}
}

func DialPassword(password string) DialOption {
	return DialOption{func(do *dialOptions) {
		do.password = password
	}}
}

func DialUseTLS(useTLS bool) DialOption {
	return DialOption{func(do *dialOptions) {
		do.useTLS = useTLS
	}}
}

func NewRedisClient(address string, options ...DialOption) (*Redis, error) {
	var err error
	var conn net.Conn

	do := dialOptions{
		dialer: &net.Dialer{
			Timeout: time.Second * 30,
		},
	}
	for _, option := range options {
		option.f(&do)
	}
	if do.useTLS {
		conn, err = tls.DialWithDialer(do.dialer, "tcp", address, &tls.Config{InsecureSkipVerify: true})
	} else {
		conn, err = do.dialer.Dial("tcp", address)
	}
	if err != nil {
		return nil, err
	}
	r := &Redis{
		conn:         conn,
		reader:       bufio.NewReaderSize(conn, utils.KB*32),
		writer:       bufio.NewWriterSize(conn, utils.KB*32),
		writeTimeout: do.writeTimeout,
		readTimeout:  do.readTimeout,
	}
	r.protoReader = proto.NewReader(r.reader)
	r.protoWriter = proto.NewWriter(r.writer)

	// auth
	if do.password != "" {
		authArgs := []any{"auth"}
		if do.username != "" {
			authArgs = append(authArgs, do.username)
		}
		authArgs = append(authArgs, do.password)
		reply, err := r.DoWithStringReply(authArgs...)
		if err != nil {
			return nil, err
		}
		if reply != "OK" {
			return nil, errors.Errorf("auth failed. reply=%s", reply)
		}
	}

	// ping to test connection
	reply, err := r.DoWithStringReply("ping")
	if err != nil {
		return nil, err
	}

	if reply != "PONG" {
		return nil, errors.Errorf("ping %s failed, reply=%s", address, reply)
	}
	return r, nil
}

func (r *Redis) Close() error {
	if r.conn == nil {
		return nil
	}
	err := r.conn.Close()
	if err != nil {
		return err
	}
	r.conn = nil
	return nil
}

func (r *Redis) DoWithStringReply(args ...any) (string, error) {
	err := r.Send(args...)
	if err != nil {
		return "", errors.Wrapf(err, "send args=%v failed", args)
	}

	replyInterface, err := r.Receive()
	if err != nil {
		return "", errors.Wrap(err, "receive failed")
	}
	reply := replyInterface.(string)
	return reply, nil
}

func (r *Redis) SetReadDeadline() {
	_ = r.conn.SetReadDeadline(time.Now().Add(r.readTimeout))
}

func (r *Redis) SetWriteDeadline() {
	_ = r.conn.SetWriteDeadline(time.Now().Add(r.writeTimeout))
}

func (r *Redis) Send(args ...any) error {
	argsInterface := make([]any, len(args))
	copy(argsInterface, args)
	if r.writeTimeout != 0 && r.conn != nil {
		r.SetWriteDeadline()
	}
	err := r.protoWriter.WriteArgs(argsInterface)
	if err != nil {
		return err
	}
	return r.flush()
}

func (r *Redis) SendBytes(buf []byte) error {
	_, err := r.writer.Write(buf)
	if err != nil {
		return err
	}
	return r.flush()
}

func (r *Redis) flush() error {
	return r.writer.Flush()
}

func (r *Redis) Receive() (any, error) {
	if !r.useMemFile && r.readTimeout != 0 {
		r.SetReadDeadline()
	}
	return r.protoReader.ReadReply()
}

func (r *Redis) GetBufioReader() *bufio.Reader {
	if r.readTimeout != 0 && r.conn != nil {
		r.SetReadDeadline()
	}
	return r.reader
}

func (r *Redis) Read(buf []byte) (int, error) {
	n, err := r.GetBufioReader().Read(buf)
	r.ReadBytesAdd(int64(n))
	return n, err
}

func (r *Redis) ReadByte() (byte, error) {
	b, err := r.GetBufioReader().ReadByte()
	if err != nil {
		return b, err
	}
	r.ReadBytesAdd(1)
	return b, nil
}

func (r *Redis) ReadString(delim byte) (string, error) {
	reply, err := r.GetBufioReader().ReadString(delim)
	r.ReadBytesAdd(int64(len(reply)))
	return reply, err
}

func (r *Redis) BufioProtoReader() *proto.Reader {
	return r.protoReader
}

func (r *Redis) SetBufioProtoReader(rd *bufio.Reader) {
	r.protoReader = proto.NewReader(rd)
}

func (r *Redis) ReadBytesAdd(n int64) {
	if n == 0 {
		return
	}
	atomic.AddInt64(&r.readBytes, n)
}

func (r *Redis) ReadBytes() int64 {
	return atomic.LoadInt64(&r.readBytes)
}

func (r *Redis) InputBytes() int64 {
	return r.ReadBytes() + r.protoReader.ReadBytes()
}

func (r *Redis) UseMemFile() {
	r.useMemFile = true
}

func (r *Redis) CancelUseMemFile() {
	r.useMemFile = false
}

func (r *Redis) GetConn() net.Conn {
	return r.conn
}
