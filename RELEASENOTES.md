## 更新级别说明
LOW：一般级别，包含日常新功能升级（例如新增某个功能）。
MEDIUM：推荐级别， 包含功能模块优化类的升级（例如优化了某个功能）。除此以外，还包含了LOW级别所包含的更新内容。
HIGH：重要级别，包含影响稳定性或安全性的重要升级（例如修复某个漏洞或缺陷）。除此以外，还包含LOW和MEDIUM级别所包含的更新内容。

## Redis-syncer 1.2 release notes
=======================
### [ redis-syncer 1.2.7] [HIGH] Release date: 14 Feb. 2025
- [add] 新增 Writer限流器, 防止断网恢复瞬间流量打爆目的端
- [add] 新增 active_addrs查看接口，方便获取对端集群实例列表
- [add] 新增 创建writer时cluster info状态检查，避免选取到非预期节点拉目的端路由
- [fix] 修复源端数据为空时，wstage状态不准的 bug
- [fix] 修复重入RegisterMetric导致coredump的 bug
- [fix] 修复未处理ECONNRESET错误导致无法重建的 bug
- [fix] 修复go-redis SDK修复Slave出现无port的地址的 bug, 更新go-redis至v8.11.11

### [ redis-syncer 1.2.6] [HIGH] Release date: 15 Oct. 2024
- [add] 新增 psync_type_total 监控, 用于查看 psync 触发的同步类型
- [add] 新增 wstage send-rdb-done 类型, 细化 writer 各阶段, 用于减少三活部署时的数据不一致窗口
- [fix] 修复部分场景下 wstage 状态机更新异常的 bug
- [fix] 修复宿主机故障等场景下, syncer 重建任务会夯住的 bug
- [fix] 修复部分场景下 go-redis 更新 remote cluster 路由可能 panic 的 bug
- [fix] 修复 /api/v1/slots 接口返回异常的 bug

### [ redis-syncer 1.2.5] [HIGH] Release date: 15 Jun. 2024
- [change] 调整 wirter 与 reader 之间的 command chan size 1024 -> 2048
- [change] 调整配置文件中 sockfile_size 默认大小 1G -> 5G
- [remove] 去掉 automaxprocs 依赖，pod 环境下可自适应 maxprocs
- [remove] 去掉了 vendor 目录，在容器环境下使用 mod 编译
- [fix] 修复了部分场景下 syncer 同步任务 exactly once 无法保证的问题
- [fix] 当 memfile 写满时，不再直接报错 no space left in file 并重建任务，而是以阻塞的方式等待故障恢复
- [perf] 替换监控库依赖库，并调整了部分 metric 策略，性能提升 40%
- [add] 新增支持从 syncer cmd 的时间作为 filter 条件
- [build] 升级 go 编译版本为 1.21
- [fix] 修复无法处理 forcefullresync reply 而陷入无限循环的 bug
- [add] 新增 auth 支持

### [ redis-syncer 1.2.4] [HIGH] Release date: 15 Dec. 2023
- [fix] 修复了 /task 接口增加同步任务时 group_id 不能等于 0 的问题。
- [fix] 修复了同步任务删除后遗留的监控脏数据问题。
- [mod] 优化了堆积数据超过配置任务处理的逻辑，从删除修改为任务重试。
- [mod] 优化了 /slots 接口显示路由信息，对于相同分片做了聚合。
- [mod] 优化了创建 w & r 的流程，创建成功后先做一次探活，提前发现异常。
- [mod] 优化了任务显示，增加 online/offline 字段，当任务异常是会被标记为 offline，不再有删除任务的逻辑。
- [mod] 优化了 sockfile 参数配置，最小为 4M。
- [mod] 优化了任务重建的逻辑，从 20 次重试改为无限重试，直至成功或者被删除。
- [mod] 优化了对 src 探活更新路由的逻辑，发现 master_link_status = down 时会等待重试，最多 30s
- [add] 新增了对 exstring 命令的支持。
- [add] 新增了任务状态 offline/online 的监控。
- [add] 新增了更多的日志，方便问题排查。

### [ redis-syncer 1.2.3] [HIGH] Release date: 11 Sep. 2023
- [fix] 修复了目标端实例故障漂移后，syncer 重建使用集群地址错误的 bug
- [add] 报警信息更加人性化，易读
- [add] 在 task 信息中新增 needrdb 字段显示
- [add] 在任务分发接口记录必要的错误信息
- [add] 新建任务 writer 时获得 cluster nodes 信息时会遍历参数数组

--[ redis-syncer 1.2.2] [LOW] Release date: 7 jul. 2023
- [add] 新增 lua 对 key 的过滤能力

--[ redis-syncer 1.2.1] [LOW] Release date: 9 jun. 2023
- [add] 新增 tairzset module 支持
- [add] error 日志与 info 日志统一到一个日志文件
- [fix] 修复 rdb restore 任务丢数据的 bug

## Redis-syncer 1.1 release notes
=======================
### [ redis-syncer 1.1.1] [HIGH] Release date: 6 may. 2023
- [fix] rdb 全量数据无法正常同步到对端

--[ redis-syncer 1.1.0] [LOW] Release date: 4 may. 2023
- [fix] 统计 cmd receive lag 时，去掉了 red color cmd
- [build] 增加了 vendor，防止离线编译失败

## Redis-syncer 1.0 release notes
=======================
### [ redis-syncer 1.0.8] [HIGH] Release date: 24 apr. 2023
- [fix] syncer写对端失败重试时会sleep 1秒，这个非常有影响syncer的性能

### [ redis-syncer 1.0.7] [HIGH] Release date: 20 apr. 2023
- [fix] 修复了处理 eval 命令计算 slot 不准的问题
- [add] go-redis 增加了关键日志，如 cmd 对应的 slot，如 MOVED 错误，日志级别为 debug
- [add] 增加了新 api /v1/slots， 用于查看各 syncer 当前缓存的路由信息，方便问题排查

### [ redis-syncer 1.0.6] [HIGH] Release date: 12 apr. 2023
- [perf] 修改底层依赖库 go-redis 获取路由的方式，由 cluster slots 命令修改为 slave 节点上执行 cluster nodes
- [perf] 提取底层依赖库 go-redis cron 路由的 period，10s -> 20s

### [ redis-syncer 1.0.5] [LOW] Release date: 27 mar. 2023
- [perf] 去掉冗余的监控曲线，降低成本
- [feat] 为了方便在线获取 syncer 版本，在 /cfg 接口的返回结果中增加版本信息

### [ redis-syncer 1.0.4] [LOW] Release date: 22 mar. 2023
- [fix] sync_latency_millis 监控修改为 Summary 类型

### [ redis-syncer 1.0.3] [LOW] Release date: 21 mar. 2023
- [fix] sync_latency_millis stage-processed-in-syncer 统计值不正常  
- [fix] sync_latency_millis bucket 调整为 1ms ~ 32s  
- [fix] 修改配置 pipeline_write_delay_msec 单位为 ms  
- [fix] 写对端 nil 错误统计异常 
- [doc] 增加监控相关内容

### [ redis-syncer 1.0.2] [MEDIUM] Release date: 3 mar. 2023
* [add] 配置项 origin_cmd_enabled，取值 yes/ no，默认为 no。
开启时，遇到非__前缀的正常同步 cmd， 会正常同步给对端。
关闭后，遇到非__前缀的正常同步 cmd，会过滤掉，不给对端同步，且打印日志“received cmd has no prefix of __, but no permit to sync it”，一分钟之内只有一条。
* [fix] 配置项 alert_enabled_new 取代原有的 alert_enabled，取值为 yes / no，默认 yes。支持在线修改，同时配置了两个配置，以 new 的值为准。
* [fix] 为性能考虑，去掉 go_memstats_xx 相关监控。
* [add] 丰富了监控项 sync_latency_millis 来显示 cmd 处理各阶段的耗时。

### [ redis-syncer 1.0.1 ] [HIGH] Release date: 1 nov. 2022
* 支持多种启动模式，http server 或者配置文件一次性任务。
* 支持多种 sync 任务，restore 和 psync。
* 支持多种目的节点类型， cluster 或 standalone。
* server 模式下支持自动适配源节点故障，主从切换后可自愈，目的端故障可自愈。
* 支持多种 filter 字段，目前支持 command/dbid/keysize/slots/keys/timestamp_ms/is_base 等，支持动态修改。
* 部分运行参数提供了 api 接口，支持动态修改。
* 支持 prom 监控。

Cheers,
kvdb