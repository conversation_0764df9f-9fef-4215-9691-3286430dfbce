package kvrockscontroller

import (
	"context"
	"fmt"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/slotrange"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"git.xiaojukeji.com/kvdb/go-redis/v8"
	"github.com/go-resty/resty/v2"
	"golang.org/x/sync/singleflight"
)

// controller api see belows:
// http://git.xiaojukeji.com/foundation/fusion/kvrocks-controller-1.0.0/blob/master/swag-docs/swagger.json

// Client interacts with the Kvrocks Controller API using resty.
type Client struct {
	restyClient *resty.Client
	namespace   string
	clusterName string

	sfGroup *singleflight.Group
	// Add authentication fields if needed, e.g., username, password, token
}

// NewClient creates a new Kvrocks Controller API client.
// baseURL should be the base URL of the Kvrocks Controller API, e.g., "http://10.179.168.254:9379".
func NewClient(baseURL string, namespace string, clusterName string) *Client {
	client := resty.New().
		SetBaseURL(baseURL).
		SetTimeout(3 * time.Second).
		SetRetryCount(3).
		SetRetryWaitTime(time.Second)

	// Add authentication setup here if needed, e.g.:
	// client.SetBasicAuth("user", "password")
	// client.SetAuthToken("token")

	return &Client{
		restyClient: client,
		namespace:   namespace,
		clusterName: clusterName,
		sfGroup:     &singleflight.Group{},
	}
}

// VersionInfo holds the response from the /api/v1/version endpoint.
type VersionInfo struct {
	Version string `json:"version"`
	GitInfo string `json:"git_info"`
}

// GetVersion fetches the version information from the Kvrocks Controller.
func (c *Client) GetVersion(ctx context.Context) (*VersionInfo, error) {
	res := struct {
		Data struct {
			VersionInfo
		} `json:"data"`
	}{}
	var errorResponse any // Define a struct if the API has a specific error format

	resp, err := c.restyClient.R().
		SetContext(ctx).                         // Use context for cancellation and timeouts
		SetHeader("Accept", "application/json"). // Set Accept header for JSON response
		SetResult(&res).                         // Expect VersionInfo on success
		SetError(&errorResponse).                // Expect this structure on error status codes
		Get("/api/v1/version")                   // Relative path to baseURL
	if err != nil {
		return nil, fmt.Errorf("failed to execute request for version: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("unexpected status code for version: %d, body: %s",
			resp.StatusCode(), resp.String())
	}

	return &res.Data.VersionInfo, nil
}

// LeaderInfo holds the response from the /api/v1/leader endpoint.
type LeaderInfo struct {
	LeaderAddr string `json:"LeaderAddr"`
	LeaderID   string `json:"LeaderID"`
}

// GetLeader fetches the leader information from the Kvrocks Controller.
func (c *Client) GetLeader(ctx context.Context) (*LeaderInfo, error) {
	var errorResponse any
	res := struct {
		Data struct {
			LeaderInfo
		} `json:"data"`
	}{}

	resp, err := c.restyClient.R().
		SetContext(ctx).
		SetResult(&res).
		SetError(&errorResponse).
		Get("/api/v1/leader")
	if err != nil {
		return nil, fmt.Errorf("failed to execute request for leader: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("unexpected status code for leader: %d, body: %s",
			resp.StatusCode(), resp.String())
	}

	return &res.Data.LeaderInfo, nil
}

type Node struct {
	ID       string `json:"id"`
	Addr     string `json:"addr"`
	Password string `json:"password"`
	Role     string `json:"role"`
	CreateAt int    `json:"created_at"`
}

// ShardInfo represents a single shard's information.
type ShardInfo struct {
	ID         int                  `json:"id"`
	Nodes      []*Node              `json:"nodes"`
	SlotRanges slotrange.SlotRanges `json:"slot_ranges"`
	Master     *Node                `json:"master"`
}

// ClusterInfo holds the detailed information for a specific cluster.
type ClusterInfo struct {
	Name    string                `json:"name"`
	Version int                   `json:"version"`
	Shards  map[string]*ShardInfo `json:"shards"`
}

// getCluster fetches detailed information for a specific cluster within a namespace.
func (c *Client) getCluster(ctx context.Context) (*ClusterInfo, error) {
	var errorResponse any
	res := struct {
		Data struct {
			ClusterInfo ClusterInfo `json:"cluster"`
		} `json:"data"`
	}{}

	resp, err := c.restyClient.R().
		SetContext(ctx).
		SetPathParams(map[string]string{ // Use path parameter substitution
			"namespace": c.namespace,
			"cluster":   c.clusterName,
		}).
		SetResult(&res).
		SetError(&errorResponse).
		Get("/api/v1/namespaces/{namespace}/clusters/{cluster}")
	if err != nil {
		return nil, utils.Errorf("failed to execute request for cluster %s in namespace %s: %w",
			c.clusterName, c.namespace, err)
	}

	if resp.IsError() {
		return nil, utils.Errorf("unexpected status code for cluster %s in namespace %s: %d, body: %s",
			c.clusterName, c.namespace, resp.StatusCode(), resp.String())
	}

	return &res.Data.ClusterInfo, nil
}

func (c *Client) Validate(ctx context.Context) error {
	_, err := c.getCluster(ctx)
	return err
}

func (c *Client) getRedisTopology(ctx context.Context) ([]redis.ClusterSlot, error) {
	clusterInfo, err := c.getCluster(ctx)
	if err != nil {
		return nil, err
	}
	if clusterInfo == nil || len(clusterInfo.Shards) == 0 {
		return nil, fmt.Errorf("no shards found in cluster %s", c.clusterName)
	}

	node2RedisNode := func(node *Node) redis.ClusterNode {
		return redis.ClusterNode{
			ID:   node.ID,
			Addr: node.Addr,
		}
	}

	slots := make([]redis.ClusterSlot, 0, len(clusterInfo.Shards))
	for _, shard := range clusterInfo.Shards {
		if shard.Master == nil || shard.Master.Addr == "" {
			logger.Warnf("shard %d in cluster %s has no master node, skipping", shard.ID, c.clusterName)
			continue
		}

		knodes := make([]redis.ClusterNode, 0, len(shard.Nodes))
		knodes = append(knodes, node2RedisNode(shard.Master))

		for _, node := range shard.Nodes {
			if node.ID == shard.Master.ID {
				continue
			}
			knodes = append(knodes, node2RedisNode(node))
		}

		for _, slotRanges := range shard.SlotRanges {
			slots = append(slots, redis.ClusterSlot{
				Start: slotRanges.Begin,
				End:   slotRanges.End,
				Nodes: knodes,
			})
		}
	}
	if len(slots) == 0 {
		return nil, utils.Errorf("no valid slots found in cluster %s", c.clusterName)
	}
	return slots, nil
}

func (c *Client) GetRedisClusterSlots(ctx context.Context) ([]redis.ClusterSlot, error) {
	// Use singleflight to avoid multiple concurrent calls to getCluster
	v, err, _ := c.sfGroup.Do("getRedisTopology", func() (any, error) {
		return c.getRedisTopology(ctx)
	})
	if err != nil {
		return nil, err
	}
	return v.([]redis.ClusterSlot), nil
}

func (c *Client) getAllAddrs(ctx context.Context) ([]string, error) {
	clusterInfo, err := c.getCluster(ctx)
	if err != nil {
		return nil, err
	}
	if clusterInfo == nil || len(clusterInfo.Shards) == 0 {
		return nil, fmt.Errorf("no shards found in cluster %s", c.clusterName)
	}

	addrs := make([]string, 0)
	for _, shard := range clusterInfo.Shards {
		for _, node := range shard.Nodes {
			if node.Addr != "" {
				addrs = append(addrs, node.Addr)
			}
		}
	}
	if len(addrs) == 0 {
		return nil, utils.Errorf("no valid addresses found in cluster %s", c.clusterName)
	}
	return addrs, nil
}

func (c *Client) GetAllAddrs(ctx context.Context) ([]string, error) {
	// Use singleflight to avoid multiple concurrent calls to getAllAddrs
	v, err, _ := c.sfGroup.Do("getAllAddrs", func() (interface{}, error) {
		return c.getAllAddrs(ctx)
	})
	if err != nil {
		return nil, err
	}
	return v.([]string), nil
}
