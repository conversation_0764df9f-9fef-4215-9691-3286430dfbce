package slotrange

import (
	"errors"
	"strconv"
)

const (
	MinSlot = 0
	MaxSlot = 1023
)

var ErrSlotOutOfRange = errors.New("slot id was out of range, should be between 0 and 1023 inclusive")

type SlotRange struct {
	Begin int `json:"begin"`
	End   int `json:"end"`
}

func NewSingleSlotRange(slot int) (*SlotRange, error) {
	if slot < MinSlot || slot > MaxSlot {
		return nil, ErrSlotOutOfRange
	}
	return &SlotRange{
		Begin: slot,
		End:   slot,
	}, nil
}

func NewSlotRange(start, stop int) (*SlotRange, error) {
	sl := &SlotRange{
		Begin: start,
		End:   stop,
	}
	if err := sl.Validate(); err != nil {
		return nil, err
	}
	return sl, nil
}

func (slotRange *SlotRange) String() string {
	if slotRange.Begin == slotRange.End {
		return strconv.Itoa(slotRange.Begin)
	}
	return strconv.Itoa(slotRange.Begin) + "-" + strconv.Itoa(slotRange.End)
}

func (slotRange *SlotRange) IsEmpty() bool {
	return slotRange.Begin == -1 && slotRange.End == -1
}

func (slotRange *SlotRange) HasOverlap(that *SlotRange) bool {
	return !(slotRange.End < that.Begin || slotRange.Begin > that.End)
}

func (slotRange *SlotRange) Contains(slot int) bool {
	return slot >= slotRange.Begin && slot <= slotRange.End
}

func (slr SlotRange) Validate() error {
	if slr.Begin < MinSlot || slr.Begin > MaxSlot || slr.End < MinSlot || slr.End > MaxSlot {
		return ErrSlotOutOfRange
	}
	if slr.Begin > slr.End {
		return errors.New("begin slot cannot be greater than end slot")
	}
	return nil
}
