package slotrange

import (
	"fmt"
	"sort"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
)

type SlotRanges []SlotRange

func (SlotRanges *SlotRanges) Contains(slot int) bool {
	for _, slotRange := range *SlotRanges {
		if slotRange.Contains(slot) {
			return true
		}
	}
	return false
}

func (sr SlotRanges) String() string {
	if len(sr) == 0 {
		return "[]"
	}
	result := "["
	for i, r := range sr {
		if i > 0 {
			result += ", "
		}
		result += r.String()
	}
	result += "]"
	return result
}

// Len, Swap, Less are for sorting
func (s SlotRanges) Len() int {
	return len(s)
}

func (s SlotRanges) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

func (s SlotRanges) Less(i, j int) bool {
	if s[i].Begin < s[j].Begin {
		return true
	}
	if s[i].Begin > s[j].Begin {
		return false
	}
	// if start is equal, sort by stop
	return s[i].End < s[j].End
}

func sortAndMerge(s SlotRanges) SlotRanges {
	if len(s) == 0 {
		return s
	}

	sort.Sort(s)

	merged := SlotRanges{}
	current := s[0]

	for i := 1; i < len(s); i++ {
		if s[i].Begin <= current.End+1 {
			if s[i].End > current.End {
				current.End = s[i].End
			}
			continue
		}

		merged = append(merged, current)
		current = s[i]
	}
	merged = append(merged, current)
	return merged
}

// sr MUST be sorted and merged
func isCoversRange(sr SlotRanges, sub SlotRange) bool {
	need := sub

	for _, r := range sr {
		if r.End < need.Begin {
			continue
		}

		if r.Begin > need.Begin {
			return false
		}

		if r.End >= need.End {
			return true
		}

		need.Begin = r.End + 1

		if need.Begin > need.End {
			return true
		}
	}
	return false
}

func (sr SlotRanges) IsEmpty() bool {
	if len(sr) == 0 {
		return true
	}
	for _, r := range sr {
		if !r.IsEmpty() {
			return false
		}
	}
	return true
}

func (sr SlotRanges) SubOneSlot(slot int) (SlotRanges, error) {
	single, err := NewSingleSlotRange(slot)
	if err != nil {
		return nil, err
	}
	return sr.SubRange(*single)
}

func (sr SlotRanges) SubRange(sub SlotRange) (SlotRanges, error) {
	sorted := sortAndMerge(sr)

	if !isCoversRange(sorted, sub) {
		return nil, fmt.Errorf("slot ranges %v does not cover %v: %w", sorted, sub, consts.ErrInvalidArgument)
	}
	result := SlotRanges{}
	for _, r := range sorted {
		// no overlap
		if r.End < sub.Begin || r.Begin > sub.End {
			result = append(result, r)
			continue
		}

		if r.Begin < sub.Begin {
			left := SlotRange{Begin: r.Begin, End: sub.Begin - 1}
			if left.Begin <= left.End {
				result = append(result, left)
			}
		}

		if r.End > sub.End {
			right := SlotRange{Begin: sub.End + 1, End: r.End}
			if right.Begin <= right.End {
				result = append(result, right)
			}
		}
	}

	if len(result) != 0 {
		result = sortAndMerge(result)
	}

	return result, nil
}

func (sr SlotRanges) AddRange(add SlotRange) SlotRanges {
	sorted := sortAndMerge(sr)
	sorted = append(sorted, add)
	return sortAndMerge(sorted)
}

func (sr SlotRanges) GetFirstSlot() (int, error) {
	if len(sr) == 0 {
		return -1, consts.ErrNotFound
	}
	return sr[0].Begin, nil
}

func (sr *SlotRanges) Count() int {
	cnt := 0
	*sr = sortAndMerge(*sr)
	for _, r := range *sr {
		cnt += r.End - r.Begin + 1
	}
	return cnt
}
