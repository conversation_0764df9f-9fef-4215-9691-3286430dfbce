package r2mgr

import (
	"context"
	"errors"
	"math"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/psyncreader"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/writebatchdecoder"
	r2writer "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_writer"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/commands"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"golang.org/x/sync/errgroup"
)

const ReportProgressIntervalSec = 1

type SyncerInfo struct {
	TableName string
	PeerAddr  string
	SendSeq   uint64
	LogNum    uint64
}

func parseSyncerInfo(rawSyncerInfo string) ([]SyncerInfo, error) {
	zero := SyncerInfo{}
	parseOneLine := func(line string) (string, SyncerInfo, error) {
		// extract "syncer0:table=co,peer=127.0.0.1:52350,send_seq=7,send_log_no=26,seq=0,log_no=0,last_key_ts=0""
		line = strings.TrimSpace(line)
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			return "", zero, utils.Errorf("invalid syncer info line: %s", line)
		}
		if len(parts[0]) == 0 || len(parts[1]) == 0 {
			return "", zero, utils.Errorf("invalid syncer info line: %s", line)
		}
		peerName := strings.TrimSpace(parts[0])
		rawFields := strings.Split(parts[1], ",")

		info := SyncerInfo{}

		for _, field := range rawFields {
			kvs := strings.SplitN(field, "=", 2)
			if len(kvs) != 2 {
				return "", zero, utils.Errorf("invalid syncer info field: %s", field)
			}

			k := strings.TrimSpace(kvs[0])
			v := strings.TrimSpace(kvs[1])

			switch k {
			case "table":
				info.TableName = v
			case "peer":
				info.PeerAddr = v
			case "seq":
				seq, err := strconv.ParseUint(v, 10, 64)
				if err != nil {
					return "", zero, utils.Errorf("invalid seq value: %s", v)
				}
				info.SendSeq = seq
			case "log_no":
				num, err := strconv.ParseUint(v, 10, 64)
				if err != nil {
					return "", zero, utils.Errorf("invalid log_no value: %s", v)
				}
				info.LogNum = num
			}

		}

		return peerName, info, nil
	}

	isCommentLine := func(s string) bool {
		s = strings.TrimSpace(s)
		return strings.HasPrefix(s, "#")
	}

	infos := []SyncerInfo{}

	lines := strings.Split(rawSyncerInfo, "\r\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		if len(line) == 0 || isCommentLine(line) {
			continue
		}

		_, info, err := parseOneLine(line)
		if err != nil {
			return nil, err
		}

		infos = append(infos, info)
	}

	return infos, nil
}

type writerCtx struct {
	writer     r2writer.StreamWriter
	ch         chan *entry.Entry
	pendingCnt *atomic.Int32
	seq        *atomic.Int64
	logNum     *atomic.Int64
}

func newWriterCtx(ctx context.Context, eg *errgroup.Group, writerFactory func() (r2writer.StreamWriter, error)) (*writerCtx, error) {
	writer, err := writerFactory()
	if err != nil {
		return nil, err
	}

	ch := make(chan *entry.Entry)

	eg.Go(func() error {
		defer writer.Close()
		err := writer.Start(ctx, ch)
		if err != nil {
			logger.Error(err)
		}
		return err
	})

	return &writerCtx{
		writer:     writer,
		ch:         ch,
		pendingCnt: &atomic.Int32{},
		seq:        &atomic.Int64{},
		logNum:     &atomic.Int64{},
	}, nil
}

func findLastSeqAndLogNum(writers []*writerCtx) (seq int64, logNum int64) {
	seq1, seq2 := int64(math.MaxInt64), int64(-1)
	log1, log2 := int64(math.MaxInt64), int64(-1)

	for _, w := range writers {
		if w.pendingCnt.Load() > 0 {
			seq1 = min(seq1, w.seq.Load())
			log1 = min(log1, w.logNum.Load())
		} else {
			seq2 = max(seq2, w.seq.Load())
			log2 = max(log2, w.logNum.Load())
		}
	}

	if seq1 == math.MaxInt64 {
		// all channels are empty
		return seq2, log2
	}

	return seq1, log1
}

func (mgr *R2SyncManager) psyncRoutine(ctx context.Context, tableInfo *TableStatus) error {
	if tableInfo == nil {
		return nil
	}

	tableName := tableInfo.Name
	mgr.wg.Add(1)
	defer mgr.wg.Done()

	startSeq := tableInfo.PsyncStatus.Seq
	log := logger.Logger().
		With("table", tableName).
		With("start_seq", startSeq)

	log.Infof("starting PSYNC")

	var err error
	eg, errCtx := errgroup.WithContext(ctx)

	defer func() {
		mgr.tableMutex.Lock()
		defer mgr.tableMutex.Unlock()
		tableInfo.State = TableStatePsyncStop

		retErr := err
		if retErr == nil {
			if ctx.Err() != nil {
				retErr = ctx.Err()
			} else {
				retErr = utils.Errorf("PSYNC for table %s stopped unexpectedly unknown reason", tableName)
			}
		}
		tableInfo.PsyncStatus.Extra = retErr.Error()
		tableInfo.PsyncStatus.EndTime = time.Now()
		log.Infof("PSYNC stopped: %v", err)
	}()

	tableInfo.MarkPsyncStart()

	writerFactory := func() (r2writer.StreamWriter, error) {
		return mgr.psyncAccumWriter(errCtx, tableName, tableInfo.limiter)
	}

	writeBatchConverter := writebatchdecoder.NewWbConverter(mgr.serverID, tableName, mgr.useQueue)

	parallel := mgr.targetCfg.Parallel
	writers := make([]*writerCtx, 0, parallel)
	lastAppliedTs := atomic.Int64{}

	for range parallel {
		w, err := newWriterCtx(errCtx, eg, writerFactory)
		if err != nil {
			err = utils.Errorf("failed to create writer for table %s: %w", tableName, err)
			return err
		}
		writers = append(writers, w)
	}

	cli, err := mgr.psyncCliFactory()
	if err != nil {
		err = utils.Errorf("failed to create psync client for table %s: %w", tableName, err)
		return err
	}
	defer cli.Close()

	log.Infof("send psync2 command, seq=%d", tableInfo.PsyncStatus.Seq)
	err = cli.Send("psync2", tableInfo.Name, tableInfo.PsyncStatus.Seq)
	if err != nil {
		err = utils.Errorf("failed to send PSYNC command for table %s, seq=%d: %w", tableName, startSeq, err)
		return err
	}

	_, err = cli.Receive()
	if err != nil {
		err = utils.Errorf("failed to receive PSYNC response for table %s: %w", tableName, err)
		return err
	}
	log.Infof("psync response received, waiting for pushing data")

	psyncEventChan := make(chan psyncreader.Event)
	reportStartChan := make(chan struct{})

	receiveRoutine := func(ctx context.Context) error {
		defer close(psyncEventChan)
		defer logger.Debugf("PSYNC response reader for table %s closed", tableName)
		logger.Infof("starting to receive PSYNC response for table %s", tableName)
		respReader := psyncreader.NewRespReaderFromClient(cli)
		err := psyncreader.StartReadPsync(ctx, respReader, psyncEventChan)
		if err != nil {
			logger.Errorf("failed to start reading PSYNC response for table %s: %s", tableName, err)
			return utils.Errorf("failed to start reading PSYNC response for table %s: %w", tableName, err)
		}
		return nil
	}

	forwardApplyCmd := func(ctx context.Context, applyCmd *psyncreader.ApplyBatchCommand) error {
		entries, err := applyCmd2Entry(writeBatchConverter, applyCmd)
		if err != nil {
			return utils.Errorf("failed to convert apply command to entry for table %s: %w", tableName, err)
		}
		if len(entries) == 0 {
			logger.Warnf("apply command for table %s has no entries, seq=%d", tableName, applyCmd.Seq)
			return nil
		}
		logger.Debugf("received apply command for table %s, seq=%d, entries=%d", tableName, applyCmd.Seq, len(entries))

		writeBatchSize := len(applyCmd.WriteBatch)
		err = mgr.memLimitMgr.Acquire(ctx, tableName, int64(writeBatchSize))
		if err != nil {
			return err
		}

		var (
			remaining   = int32(len(entries))
			releaseOnce = sync.Once{}
		)

		for _, entry := range entries {
			if entry == nil {
				logger.Warnf("received nil entry for table %s, seq=%d", tableName, applyCmd.Seq)
				atomic.AddInt32(&remaining, -1)
				continue
			}

			hash := commands.Crc16(entry.Key)
			index := hash % uint16(len(writers))
			entry := entry // avoid closure capture
			hasRun := atomic.Bool{}
			targetWriter := writers[index]
			targetWriter.pendingCnt.Add(1)

			entry.Consumed = func() {
				swapped := hasRun.CompareAndSwap(false, true)
				entry := entry
				seq := int64(entry.LastSeq)
				logNum := entry.LogNum

				if !swapped {
					logger.Warnf("entry for table %s, seq=%d, log_num=%d, hash=%d, index=%d already consumed", tableName, seq, logNum, hash, index)
					return
				}

				select {
				case reportStartChan <- struct{}{}:
				default:
				}

				targetWriter.seq.Store(seq)
				targetWriter.logNum.Store(logNum)
				targetWriter.pendingCnt.Add(-1)
				lastAppliedTs.Store(time.Now().UnixMicro())

				if atomic.AddInt32(&remaining, -1) > 0 {
					return
				}

				releaseOnce.Do(func() {
					mgr.memLimitMgr.Release(tableName, int64(writeBatchSize))
					logger.Debugf("released memory for table %s, seq=%d, log_num=%d, hash=%d, index=%d, remaining=%d", tableName, applyCmd.Seq, applyCmd.LogNum, hash, index, remaining)
				})
			}

			if err := utils.SendWithContext(ctx, targetWriter.ch, entry); err != nil {
				logger.Infof("failed to forward entry for table %s, seq=%d, log_num=%d, hash=%d, index=%d: ctx.Err() = %v", tableName, applyCmd.Seq, applyCmd.LogNum, hash, index, err)
				return err
			}
			logger.Debugf("forwarded entry for table %s, seq=%d, log_num=%d, hash=%d, index=%d", tableName, applyCmd.Seq, applyCmd.LogNum, hash, index)
		}
		return nil
	}

	dispatcherRoutine := func(ctx context.Context) error {
		for event := range psyncEventChan {
			switch event.Type {
			case psyncreader.EventTypeApplyBatch:
				applyCmd := event.Payload.(*psyncreader.ApplyBatchCommand)
				err = forwardApplyCmd(ctx, applyCmd)
				if err != nil {
					return err
				}
			case psyncreader.EventTypePing:
				logger.Info("received PING event, ignored")
			default:
				return utils.Errorf("unknown PSYNC event type: %s", event.Type)
			}
		}
		logger.Infof("PSYNC event channel closed for table %s", tableName)
		return nil
	}

	reportProgressRoutine := func(ctx context.Context) error {
		report := func(logNum, seq, lastTs int64) error {
			resp := mgr.sourceClient.Do(ctx, "replconf", "syncer", tableName, logNum, seq, lastTs)
			if resp.Err() != nil {
				return utils.Errorf("failed to report progress for table %s: %w", tableName, resp.Err())
			}
			logger.Infof("reported progress for table %s, seq=%d, log_num=%d", tableName, seq, logNum)
			return nil
		}

		logger.Infof("starting progress routine for table %s, with start seq %d", tableName, startSeq)
		if report(0, 0, time.Now().UnixMicro()) != nil {
			return err
		}

		logger.Infof("progress routine for table %s start, waiting for first apply command", tableName)
		select {
		case _, ok := <-reportStartChan:
			if !ok {
				logger.Infof("report start channel for table %s closed before receiving first apply command", tableName)
				return nil
			}
		case <-ctx.Done():
			return nil
		}
		logger.Infof("progress routine started for table %s", tableName)

		ticker := time.NewTicker(ReportProgressIntervalSec * time.Second)
		defer ticker.Stop()

		lastSeq := int64(-1)
		lastLogNum := int64(-1)

		for {
			select {
			case <-ctx.Done():
				return nil
			case <-ticker.C:
				seq, logNum := findLastSeqAndLogNum(writers)

				if seq == lastSeq && logNum == lastLogNum {
					logger.Debugf("no progress for table %s, seq=%d, log_num=%d", tableName, seq, logNum)
					continue
				}

				lastSeq, lastLogNum = seq, logNum
				if err := report(logNum, seq, lastAppliedTs.Load()); err != nil {
					return err
				}

				go tableInfo.SetPsyncStatus(uint64(seq), uint64(logNum), lastAppliedTs.Load())
			}
		}
	}

	eg.Go(func() error {
		err := receiveRoutine(errCtx)
		if err != nil {
			logger.Error(err)
		}
		return err
	})
	eg.Go(func() error {
		err := dispatcherRoutine(errCtx)
		if err != nil {
			logger.Error(err)
		}
		return err
	})
	eg.Go(func() error {
		err := reportProgressRoutine(errCtx)
		if err != nil {
			logger.Error(err)
		}
		return err
	})

	err = eg.Wait()
	return err
}

func applyCmd2Entry(converter *writebatchdecoder.WbConverter, cmd *psyncreader.ApplyBatchCommand) ([]*entry.Entry, error) {
	wb, err := cmd.ToWriteBatch()
	if err != nil {
		return nil, err
	}
	return converter.ConvertWb2Entry(cmd.Seq, cmd.LogNum, wb)
}

func (mgr *R2SyncManager) recoverPsync(ctx context.Context) error {
	stopped := mgr.getStoppedTableByUser()

	resp := mgr.sourceClient.Info(ctx, "syncer")
	if resp.Err() != nil {
		return utils.Errorf("failed to get syncer info: %s", resp.Err())
	}

	rawSyncerInfo := resp.Val()
	psyncInfos, err := parseSyncerInfo(rawSyncerInfo)
	if err != nil {
		return err
	}

	mgr.tableMutex.Lock()
	locked := true
	defer func() {
		if locked {
			mgr.tableMutex.Unlock()
		}
	}()
	unlock := func() {
		if locked {
			mgr.tableMutex.Unlock()
			locked = false
		}
	}

	pendingTables := make(map[string]uint64)

	for _, psyncInfo := range psyncInfos {
		if psyncInfo.TableName == "" || psyncInfo.PeerAddr == "" {
			logger.Warnf("invalid syncer info: %v", psyncInfo)
			continue
		}

		if _, exist := stopped[psyncInfo.TableName]; exist {
			logger.Infof("table %s is stopped by user, skipping recovery", psyncInfo.TableName)
			continue
		}

		tableName := psyncInfo.TableName
		seq := psyncInfo.SendSeq

		logger.Infof("found PSYNC info from r2 for table %s, peer=%s, send_seq=%d, log_num=%d",
			tableName, psyncInfo.PeerAddr, psyncInfo.SendSeq, psyncInfo.LogNum)

		pendingTables[tableName] = seq
	}

	if len(pendingTables) == 0 {
		logger.Info("no pending PSYNC tables found, skipping recovery")
		return nil
	}

	unlock()

	for tableName, lastSeq := range pendingTables {
		logger.Infof("starting PSYNC recovery for table %s, last_seq=%d", tableName, lastSeq)
		mgr.startPsync(tableName, lastSeq, nil)
	}

	return nil
}

func (mgr *R2SyncManager) startPsync(tableName string, lastSeq uint64, tableStatus *TableStatus) {
	if tableStatus == nil {
		tableStatus = mgr.makeTableStatus(tableName)
		tableStatus.State = TableStatePsyncing

		mgr.tableMutex.Lock()
		mgr.tables[tableName] = tableStatus
		mgr.tableMutex.Unlock()
	}

	routineCtx, cancel := context.WithCancelCause(mgr.ctx)

	tableStatus.Lock()
	defer tableStatus.Unlock()

	mgr.removeStoppedTableByUserMark(tableName)
	tableStatus.State = TableStatePsyncing
	tableStatus.cancel = cancel
	tableStatus.PsyncStatus.Seq = lastSeq
	tableStatus.PsyncStatus.StartTime = time.Now()

	psyncWrapper := func() {
		defer cancel(nil)
		for {
			// psync should run forever until stopped by user
			// so we use a loop here

			err := mgr.psyncRoutine(routineCtx, tableStatus)
			if errors.Is(err, consts.ErrStoppedByUser) {
				logger.Infof("PSYNC for table %s stopped by user", tableName)
				return
			}

			if utils.IsCanceled(routineCtx) != nil {
				logger.Infof("PSYNC for table %s is canceled by upper layer context", tableName)
				return
			}

			logger.Errorf("PSYNC for table %s failed: %s, retrying...", tableName, err)
			time.Sleep(gPsyncRetryIntervalSec * time.Second)
		}

	}
	go psyncWrapper()
}
