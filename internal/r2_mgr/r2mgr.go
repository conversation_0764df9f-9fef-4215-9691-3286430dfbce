package r2mgr

import (
	"context"
	"os"
	"path"
	"sync"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/kvrockscontroller"
	r2config "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_config"
	r2writer "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_writer"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/client"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"git.xiaojukeji.com/kvdb/go-redis/v8"
	"golang.org/x/time/rate"
)

const gCkptSyncDeamonIntervalSec = 5
const gCkptSyncRetryIntervalSec = 3
const gPsyncRetryIntervalSec = 3
const gEntryChanSize = 2048

type streamWriterFactory func(ctx context.Context, tableName string, limiter *rate.Limiter) (r2writer.StreamWriter, error)

type R2SyncManager struct {
	// Table name -> TableInfo
	tables     map[string]*TableStatus
	tableMutex sync.Mutex

	rateLimitMgr *RateLimitConfigMgr
	memLimitMgr  *MemMgrLimiter

	sourceClient *redis.Client
	sourceCfg    r2config.SourceConfig

	targetCfg              r2config.TargetConfig
	targetControllerClient *kvrockscontroller.Client

	cfg r2config.Config

	serverID      string
	ckptBatchSize int
	useQueue      bool

	ckptDispatcherFactory streamWriterFactory
	psyncAccumWriter      streamWriterFactory

	psyncCliFactory func() (*client.Redis, error)

	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

func NewR2SyncManager(ctx context.Context, config r2config.Config, rateLimitMgr *RateLimitConfigMgr, memLimiter *MemMgrLimiter) (*R2SyncManager, error) {
	sourceCfg := config.Source
	targetCfg := config.Target
	ckptBatchSize := config.General.CkptBatchSize

	var err error
	ctx, cancel := context.WithCancel(ctx)
	defer func() {
		if err == nil {
			return
		}
		cancel()
	}()

	readerTimeout := time.Duration(sourceCfg.ReadTimeoutSec) * time.Second
	sourceAddr := sourceCfg.Addr

	kvController := kvrockscontroller.NewClient(targetCfg.Addr, targetCfg.Ns, targetCfg.ClusterName)
	if err := kvController.Validate(ctx); err != nil {
		err = utils.Errorf("failed to validate kvrocks controller: %w", err)
		logger.Errorf("%s", err)
		return nil, err
	}

	ckptDispfactory := makeCkptDispatcherFactory(targetCfg, kvController)
	psyncStremWriter := makePsyncDispatcherFactory(targetCfg, kvController)

	psyncClifactory := func() (*client.Redis, error) {
		cli, err := client.NewRedisClient(sourceAddr, client.DialConnectTimeoutDuration(readerTimeout), client.DialConnectTimeoutDuration(readerTimeout))
		return cli, err
	}

	client := redis.NewClient(&redis.Options{
		Addr:     sourceAddr,
		PoolSize: 1,
	})

	mgr := &R2SyncManager{
		tables:                 make(map[string]*TableStatus),
		tableMutex:             sync.Mutex{},
		rateLimitMgr:           rateLimitMgr,
		memLimitMgr:            memLimiter,
		sourceClient:           client,
		sourceCfg:              sourceCfg,
		targetCfg:              targetCfg,
		cfg:                    config,
		targetControllerClient: kvController,
		serverID:               sourceCfg.ServerID,
		ckptBatchSize:          ckptBatchSize,
		useQueue:               sourceCfg.UseQueue,
		ckptDispatcherFactory:  ckptDispfactory,
		psyncAccumWriter:       psyncStremWriter,
		psyncCliFactory:        psyncClifactory,
		ctx:                    ctx,
		cancel:                 cancel,
	}

	err = mgr.removeAllCkpts()
	if err != nil {
		logger.Errorf("failed to remove all checkpoints: %s", err)
		return nil, err
	}

	err = mgr.recoverPsync(ctx)
	if err != nil {
		logger.Errorf("failed to recover PSYNC: %s", err)
		return nil, err
	}

	go mgr.ckptSyncDeamonRoutine(ctx)
	logger.Info("R2SyncManager initialized")

	return mgr, nil
}

func (mgr *R2SyncManager) Close() {
	logger.Info("closing R2SyncManager")
	mgr.cancel()
	mgr.wg.Wait()
	if mgr.sourceClient != nil {
		if err := mgr.sourceClient.Close(); err != nil {
			logger.Errorf("failed to close source client: %s", err)
		}
	}
}

func (mgr *R2SyncManager) AddPsync(ctx context.Context, tableName string) error {
	// If there is an doing task
	//   i. if the force is false -> return an error
	//   ii. if the force is true -> cancel the existing task and remove it from the map, then
	//       query the lastseq from source client and start a new PSYNC task
	// If there is no doing task
	//   i. if there is a finished checkpoint task, query the last sequence from the checkpoint tracker
	//   ii. if there is no finished checkpoint task, query the last sequence from the source client, then
	//       start a new PSYNC task
	var lastSeq *uint64
	mgr.tableMutex.Lock()
	curTable, exist := mgr.tables[tableName]
	mgr.tableMutex.Unlock()

	if exist {
		if IsTableStateDoing(curTable.State) {
			return utils.Errorf("table %s sync already exists, current state: %s", tableName, curTable.State)
		} else if curTable.State == TableStateCKPTSyncDone {
			seq := curTable.CkptStatus.Seq
			logger.Infof("table %s has a completed checkpoint, starting PSYNC from seq %d", tableName, seq)
			lastSeq = &seq
		}
	}

	if lastSeq == nil {
		// If there is no existing task, we need to query the last sequence number
		resp, err := mgr.sourceClient.Do(ctx, "getsyncerseq", tableName).Int64()
		if err != nil {
			return utils.Errorf("failed to get syncer seq for table %s: %w", tableName, err)
		}
		if resp < 0 {
			return utils.Errorf("failed to get syncer seq for table %s: invalid sequence number %d, maybe the table not exists", tableName, resp)
		}
		seq := uint64(resp)
		lastSeq = &seq
	}

	mgr.startPsync(tableName, *lastSeq, nil)
	return nil
}

func (mgr *R2SyncManager) GetAllTables() ([]*TableStatus, error) {
	mgr.tableMutex.Lock()
	defer mgr.tableMutex.Unlock()

	tables := make([]*TableStatus, 0, len(mgr.tables))
	for _, info := range mgr.tables {
		tables = append(tables, info.Copy())
	}
	return tables, nil
}

func (mgr *R2SyncManager) GetTableInfo(tableName string) (*TableStatus, error) {
	mgr.tableMutex.Lock()
	defer mgr.tableMutex.Unlock()

	info, ok := mgr.tables[tableName]
	if !ok {
		return nil, consts.ErrNotFound
	}
	return info.Copy(), nil
}

func (mgr *R2SyncManager) StopTable(tableName string) error {
	mgr.tableMutex.Lock()
	defer mgr.tableMutex.Unlock()

	info, ok := mgr.tables[tableName]
	if !ok {
		return consts.ErrNotFound
	}

	if info.State == TableStateCKPTSyncDoing || info.State == TableStateCKPTSyncDone || info.State == TableStateCKPTSyncFailed {
		return utils.Errorf("cannot stop checkpoint sync for table %s, current state: %s", tableName, info.State)
	}

	if info.cancel != nil {
		info.cancel(consts.ErrStoppedByUser)
	}

	return nil
}

func (mgr *R2SyncManager) removeStoppedTableByUserMark(tableName string) error {
	dir := path.Join(mgr.cfg.General.Dir, "stopped")
	stopFile := path.Join(dir, tableName+".stop")
	if err := os.Remove(stopFile); err != nil {
		err = utils.Errorf("failed to remove stop file for table %s: %w", tableName, err)
		logger.Errorf("%s", err)
	}
	return nil
}

func (mgr *R2SyncManager) getStoppedTableByUser() map[string]struct{} {
	stoppedTables := make(map[string]struct{})
	dir := path.Join(mgr.cfg.General.Dir, "stopped")
	files, err := os.ReadDir(dir)
	if err != nil {
		logger.Warnf("failed to read stopped directory %s: %s", dir, err)
		return stoppedTables
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}
		tableName := file.Name()
		if len(tableName) > 5 && tableName[len(tableName)-5:] == ".stop" {
			stoppedTables[tableName[:len(tableName)-5]] = struct{}{}
		}
	}
	return stoppedTables
}

func (mgr *R2SyncManager) markPsyncStopByUser(tableName string) error {
	dir := path.Join(mgr.cfg.General.Dir, "stopped")
	// just make a file named <tableName>.stop
	if err := os.MkdirAll(dir, 0755); err != nil {
		return utils.Errorf("failed to create stopped directory: %w", err)
	}
	stopFile := path.Join(dir, tableName+".stop")
	if err := os.WriteFile(stopFile, []byte("stopped by user"), 0644); err != nil {
		return utils.Errorf("failed to write stop file for table %s: %w", tableName, err)
	}
	return nil
}
