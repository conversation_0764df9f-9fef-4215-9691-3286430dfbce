package r2mgr

import (
	"context"
	"sync"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"golang.org/x/time/rate"
)

type TableState string

const (
	TableStateCKPTSyncInit   TableState = "ts_ckptsync_init"
	TableStateCKPTSyncDoing  TableState = "ts_ckptsync_doing"
	TableStateCKPTSyncDone   TableState = "ts_ckptsync_done"
	TableStateCKPTSyncFailed TableState = "ts_ckptsync_failed"

	TableStatePsyncing  TableState = "ts_psyncing"
	TableStatePsyncStop TableState = "ts_psync_stop"
)

func IsTableStateDoing(state TableState) bool {
	return state == TableStateCKPTSyncDoing || state == TableStatePsyncing
}

func IsCkptSyncState(state TableState) bool {
	return state == TableStateCKPTSyncDoing || state == TableStateCKPTSyncDone || state == TableStateCKPTSyncFailed
}

func IsPsyncState(state TableState) bool {
	return state == TableStatePsyncing || state == TableStatePsyncStop
}

type TableStatus struct {
	Name  string     `json:"table_name"`
	State TableState `json:"state"`

	PsyncStatus *PsyncStatus    `json:"psync_status,omitempty"`
	CkptStatus  *CkptSyncStatus `json:"ckpt_status,omitempty"`

	sync.Mutex `json:"-"`
	limiter    *rate.Limiter           `json:"-"`
	cancel     context.CancelCauseFunc `json:"-"`
}

type PsyncStatus struct {
	Seq       uint64    `json:"seq"`
	LogNum    uint64    `json:"log_num"`
	LastTs    int64     `json:"last_ts"`
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Extra     string    `json:"extra,omitempty"`
}

type CkptSyncStatus struct {
	Seq               uint64     `json:"seq"`
	TargetSlotRanges  SlotRanges `json:"target_slot_ranges"`
	PendingSlotRanges SlotRanges `json:"pending_slot_ranges"`
	StartTime         time.Time  `json:"start_time,omitempty"`
	EndTime           time.Time  `json:"end_time,omitempty"`
	Extra             string     `json:"extra,omitempty"`
}

func (mgr *R2SyncManager) makeTableStatus(tableName string) *TableStatus {
	_, rateLimiter := mgr.rateLimitMgr.GetRateLimit(tableName, true)

	return &TableStatus{
		Name:        tableName,
		State:       TableStateCKPTSyncInit,
		CkptStatus:  &CkptSyncStatus{},
		PsyncStatus: &PsyncStatus{},
		limiter:     rateLimiter,
		cancel: func(cause error) {
			logger.Infof("default cancel function called for table %s with cause: %v", tableName, cause)
		},
	}
}

func (ts *TableStatus) SetPsyncStatus(seq uint64, logNum uint64, lastTs int64) {
	ts.Lock()
	defer ts.Unlock()

	if ts.PsyncStatus == nil {
		logger.Errorf("PsyncStatus is nil for table %s, cannot set status", ts.Name)
		return
	}

	ts.PsyncStatus.Seq = seq
	ts.PsyncStatus.LogNum = logNum
	ts.PsyncStatus.LastTs = lastTs

}

func (ts *TableStatus) Copy() *TableStatus {
	ts.Lock()
	defer ts.Unlock()

	copied := &TableStatus{
		Name:  ts.Name,
		State: ts.State,
	}

	copied.CkptStatus = &CkptSyncStatus{
		Seq:               ts.CkptStatus.Seq,
		TargetSlotRanges:  ts.CkptStatus.TargetSlotRanges,
		PendingSlotRanges: ts.CkptStatus.PendingSlotRanges,
		StartTime:         ts.CkptStatus.StartTime,
		EndTime:           ts.CkptStatus.EndTime,
		Extra:             ts.CkptStatus.Extra,
	}

	if !ts.PsyncStatus.StartTime.IsZero() {
		copied.PsyncStatus = &PsyncStatus{
			Seq:       ts.PsyncStatus.Seq,
			LogNum:    ts.PsyncStatus.LogNum,
			LastTs:    ts.PsyncStatus.LastTs,
			StartTime: ts.PsyncStatus.StartTime,
			EndTime:   ts.PsyncStatus.EndTime,
			Extra:     ts.PsyncStatus.Extra,
		}
	}

	return copied
}

func (ts *TableStatus) MarkCkptSlotDone(slot int) {
	ts.Lock()
	defer ts.Unlock()

	if !IsCkptSyncState(ts.State) {
		logger.Errorf("cannot mark checkpoint slot %d done for table %s, current state: %s", slot, ts.Name, ts.State)
		return
	}

	if ts.CkptStatus == nil {
		logger.Errorf("cannot mark checkpoint slot %d done for table %s, CkptStatus is nil", slot, ts.Name)
		return
	}

	srs, err := ts.CkptStatus.PendingSlotRanges.SubOneSlot(slot)
	if err != nil {
		logger.Errorf("failed to subtract slot %d from pending slot ranges for table %s: %s", slot, ts.Name, err)
		return
	}

	ts.CkptStatus.PendingSlotRanges = srs
	if ts.CkptStatus.PendingSlotRanges.IsEmpty() {
		logger.Infof("all checkpoint slots done for table %s, marking as done", ts.Name)
		ts.State = TableStateCKPTSyncDone
		ts.CkptStatus.EndTime = time.Now()
	} else {
		logger.Infof("checkpoint slot %d done for table %s, remaining pending slots: %s", slot, ts.Name, ts.CkptStatus.PendingSlotRanges)
	}
}

func (ts *TableStatus) MarkCkptSyncStart() {
	ts.Lock()
	defer ts.Unlock()

	if ts.CkptStatus == nil {
		logger.Errorf("CkptStatus is nil for table %s, cannot mark sync start", ts.Name)
	}

	ts.CkptStatus.StartTime = time.Now()
	ts.State = TableStateCKPTSyncDoing
}

func (ts *TableStatus) MarkPsyncStart() {
	ts.Lock()
	defer ts.Unlock()

	if ts.PsyncStatus == nil {
		logger.Errorf("PsyncStatus is nil for table %s, cannot mark sync start", ts.Name)
		return
	}

	ts.PsyncStatus.StartTime = time.Now()
	ts.State = TableStatePsyncing
}

func (ts *TableStatus) MarkCkptFail(extra string) {
	ts.Lock()
	defer ts.Unlock()

	if ts.CkptStatus == nil {
		logger.Errorf("CkptStatus is nil for table %s, cannot mark sync fail", ts.Name)
		return
	}

	ts.State = TableStateCKPTSyncFailed
	ts.CkptStatus.EndTime = time.Now()
	ts.CkptStatus.Extra = extra
	logger.Errorf("checkpoint sync for table %s failed", ts.Name)
}

func (ts *TableStatus) IsCkptPendingSlotEmpty() bool {
	ts.Lock()
	defer ts.Unlock()

	if ts.CkptStatus == nil {
		logger.Errorf("CkptStatus is nil for table %s, cannot check pending slots", ts.Name)
		return true
	}

	return ts.CkptStatus.PendingSlotRanges.IsEmpty()
}
