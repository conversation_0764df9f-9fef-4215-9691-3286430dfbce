package r2mgr

import (
	"context"
	"sync/atomic"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/kvrockscontroller"
	r2config "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_config"
	r2writer "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_writer"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/slotrange"
	"golang.org/x/time/rate"
)

func makeCkptDispatcherFactory(targetCfg r2config.TargetConfig, kvController *kvrockscontroller.Client) func(ctx context.Context, tableName string, limiter *rate.Limiter) (r2writer.StreamWriter, error) {
	return func(ctx context.Context, tableName string, limiter *rate.Limiter) (r2writer.StreamWriter, error) {
		redisWriter, err := r2writer.NewRedisClusterWriter(ctx, tableName, &targetCfg, limiter, kvController)
		if err != nil {
			return nil, err
		}

		dispather := r2writer.NewSimpleDispatcher(redisWriter, targetCfg.Parallel)
		return dispather, nil
	}
}

func makePsyncDispatcherFactory(targetCfg r2config.TargetConfig, kvController *kvrockscontroller.Client) func(ctx context.Context, tableName string, limiter *rate.Limiter) (r2writer.StreamWriter, error) {
	return func(ctx context.Context, tableName string, limiter *rate.Limiter) (r2writer.StreamWriter, error) {
		redisWriter, err := r2writer.NewRedisClusterWriter(ctx, tableName, &targetCfg, limiter, kvController)
		if err != nil {
			return nil, err
		}

		dispather := r2writer.NewAccumuStreamWriter(redisWriter, time.Duration(targetCfg.PipelineWriteDelayMs)*time.Millisecond, int(targetCfg.PipelineWriteBatch))
		return dispather, nil
	}
}

type AtomicTime = atomic.Pointer[time.Time]
type SlotRange = slotrange.SlotRange
type SlotRanges = slotrange.SlotRanges
