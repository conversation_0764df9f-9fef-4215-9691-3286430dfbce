package r2mgr

import (
	"context"
	"sync/atomic"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/prometheus/client_golang/prometheus"
	"golang.org/x/sync/semaphore"
)

// Prometheus metrics
var (
	memUsage = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "r2mgr_mem_usage_bytes",
			Help: "Current memory usage in bytes per table",
		},
		[]string{"table_name"},
	)
	onDemandMem = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "r2mgr_on_demand_mem_bytes",
			Help: "Memory currently being acquired but not yet satisfied per table",
		},
		[]string{"table_name"},
	)
	memLimit = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "r2mgr_mem_limit_bytes",
			Help: "Total memory limit in bytes",
		},
	)
)

func init() {
	prometheus.MustRegister(memUsage)
	prometheus.MustRegister(onDemandMem)
	prometheus.MustRegister(memLimit)
}

type MemUsage struct {
	Used     atomic.Int64
	OnDemand atomic.Int64 // Memory that is currently being acquired but not yet satisfied
}

type MemMgrLimiter struct {
	sem        *semaphore.Weighted
	statistics utils.Map[string, *MemUsage]
}

func NewMemMgrLimiter(maxMem int64) *MemMgrLimiter {
	logger.Infof("Initializing memory manager limiter with max memory: %d MiB", maxMem>>20)
	memLimit.Set(float64(maxMem))
	return &MemMgrLimiter{
		sem:        semaphore.NewWeighted(maxMem),
		statistics: utils.Map[string, *MemUsage]{},
	}
}

func (m *MemMgrLimiter) SetNewMemLimit(maxMem int64) {
	memLimit.Set(float64(maxMem)) // 更新内存限制指标
	m.sem = semaphore.NewWeighted(maxMem)
}

func (m *MemMgrLimiter) Acquire(ctx context.Context, tableName string, memSize int64) error {
	if memSize < 0 {
		return utils.Errorf("acquired mem size can't be negtive")
	}

	if memSize == 0 {
		logger.Warnf("acquired mem size is 0 for table %s, skipping", tableName)
		return nil
	}

	counter, loaded := m.statistics.Load(tableName)
	if !loaded {
		counter = &MemUsage{}
		m.statistics.LoadOrStore(tableName, counter)
		logger.Debugf("Initialized memory usage counter for table %s", tableName)
		return m.Acquire(ctx, tableName, memSize)
	}

	counter.OnDemand.Add(memSize)
	onDemandMem.WithLabelValues(tableName).Add(float64(memSize))

	logger.Debugf("Acquiring %d bytes of memory for table %s, current on-demand memory: %d bytes", memSize, tableName, counter.OnDemand.Load())
	if err := m.sem.Acquire(ctx, memSize); err != nil {
		counter.OnDemand.Add(-memSize)
		onDemandMem.WithLabelValues(tableName).Add(-float64(memSize))
		return utils.Errorf("failed to acquire memory for %s: %w", tableName, err)
	}
	logger.Debugf("Successfully acquired %d bytes of memory for table %s", memSize, tableName)

	counter.OnDemand.Add(-memSize)
	onDemandMem.WithLabelValues(tableName).Add(-float64(memSize))
	counter.Used.Add(memSize)
	memUsage.WithLabelValues(tableName).Add(float64(memSize))
	return nil
}

func (m *MemMgrLimiter) Release(key string, memSize int64) {
	counter, _ := m.statistics.Load(key)
	counter.Used.Add(-memSize)
	memUsage.WithLabelValues(key).Add(-float64(memSize))
	m.sem.Release(memSize)
	logger.Debugf("Released %d bytes of memory for table %s, current used memory: %d bytes", memSize, key, counter.Used.Load())
}
