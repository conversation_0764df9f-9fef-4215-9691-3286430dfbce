package r2mgr

import (
	"context"
	"os"
	"path"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/checkpoint"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

const (
	CkptBaseDir = "ckpt"
	CkptDataDir = "data"
)

type CkptPersistData struct {
	TableName         string     `json:"table_name"`
	TargetSlotRanges  SlotRanges `json:"target_slot_ranges"`
	PendingSlotRanges SlotRanges `json:"pending_slot_ranges"`
	StartTime         time.Time  `json:"start_time,omitempty"`
}

type CheckPoint = checkpoint.CheckPoint

func (mgr *R2SyncManager) createCkpt(ctx context.Context, tableName string) (string, error) {
	resp, err := mgr.sourceClient.Do(ctx, "create_table_checkpoint", tableName).Text()
	if err != nil {
		return "", utils.Errorf("failed to create checkpoint for table %s: %w", tableName, err)
	}

	tableDir := path.Join(CkptBaseDir, tableName)
	if err := os.MkdirAll(tableDir, 0755); err != nil {
		return "", utils.Errorf("failed to create directory %s for table %s: %w", tableDir, tableName, err)
	}

	ckptPath := path.Join(tableDir, CkptDataDir)
	if err := os.Rename(resp, ckptPath); err != nil {
		return "", utils.Errorf("failed to move checkpoint data to %s for table %s: %w", ckptPath, tableName, err)
	}
	return ckptPath, nil
}

func (mgr *R2SyncManager) getCkpt(tableName string) (*CheckPoint, error) {
	ckptPath := path.Join(CkptBaseDir, tableName, CkptDataDir)
	return checkpoint.OpenCheckpointDB(ckptPath)
}

func (mgr *R2SyncManager) removeCkpt(tableName string) error {
	ckptPath := path.Join(CkptBaseDir, tableName, CkptDataDir)
	if err := os.RemoveAll(ckptPath); err != nil {
		return utils.Errorf("failed to remove checkpoint for table %s at %s: %w", tableName, ckptPath, err)
	}
	// logger.Infof("(NOP FOR DEBUG)removed checkpoint for table %s at %s", tableName, ckptPath)
	logger.Infof("removed checkpoint for table %s at %s", tableName, ckptPath)
	return nil
}

func (mgr *R2SyncManager) removeAllCkpts() error {
	if err := os.RemoveAll(CkptBaseDir); err != nil {
		return utils.Errorf("failed to remove all checkpoints at %s: %w", CkptBaseDir, err)
	}
	// logger.Infof("(NOP FOR DEBUG)removed all checkpoints at %s", CkptBaseDir)
	logger.Infof("removed all checkpoints at %s", CkptBaseDir)
	return nil
}
