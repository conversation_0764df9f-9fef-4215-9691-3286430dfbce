package r2mgr

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	pseudohlc "git.xiaojukeji.com/fusion/fusion-syncer/internal/pseudo_hlc"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/checkpointdecoder"
	r2writer "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_writer"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"golang.org/x/sync/errgroup"
)

func (mgr *R2SyncManager) IsSourceClientMaster(ctx context.Context) (bool, error) {
	const maxRetries = 5
	const interval = 100 * time.Millisecond

	parseRole := func(resp string) (string, bool) {
		for _, line := range strings.Split(resp, "\r\n") {
			if parts := strings.SplitN(line, ":", 2); len(parts) == 2 {
				key := strings.ToLower(strings.TrimSpace(parts[0]))
				if key == "role" {
					return strings.ToLower(strings.TrimSpace(parts[1])), true
				}
			}
		}
		return "", false
	}

	var check func(int) (bool, error)
	check = func(attempt int) (bool, error) {
		if attempt >= maxRetries {
			finalResp, err := mgr.sourceClient.Info(ctx, "replication").Result()
			if err != nil {
				return false, fmt.Errorf("final retry failed after %d attempts: %w", maxRetries, err)
			}

			if role, ok := parseRole(finalResp); ok {
				return role == "master", nil
			}

			logger.Warnf("empty replication info after %d attempts, assuming master (stand alone mode)", maxRetries)
			return true, nil
		}

		if err := ctx.Err(); err != nil {
			return false, err
		}

		resp, err := mgr.sourceClient.Info(ctx, "replication").Result()
		if err != nil {
			logger.Warnf("failed to get replication info (attempt %d/%d): %s", attempt+1, maxRetries, err)
			time.Sleep(interval)
			return check(attempt + 1)
		}

		if role, ok := parseRole(resp); ok {
			return role == "master", nil
		}

		if resp == "" {
			logger.Warnf("empty replication info (attempt %d/%d), retrying", attempt+1, maxRetries)
		} else {
			logger.Warnf("missing role field in replication info (attempt %d/%d), retrying", attempt+1, maxRetries)
		}

		time.Sleep(interval)
		return check(attempt + 1)
	}

	return check(0)
}

func (mgr *R2SyncManager) ckptSyncDeamonRoutine(ctx context.Context) {
	mgr.wg.Add(1)
	defer mgr.wg.Done()

	ticker := time.NewTicker(gCkptSyncDeamonIntervalSec * time.Second)
	defer ticker.Stop()

	filterCkptSyncTables := func() map[string]*TableStatus {
		mgr.tableMutex.Lock()
		defer mgr.tableMutex.Unlock()
		filtered := make(map[string]*TableStatus)
		for name, tableInfo := range mgr.tables {
			if tableInfo.State == TableStateCKPTSyncDoing {
				filtered[name] = tableInfo
			}
		}
		return filtered
	}

	abortOngoingSyncs := func(tables map[string]*TableStatus) {
		err := fmt.Errorf("source <%s> is not master, aborting ongoing checkpoint syncs: %w", mgr.sourceCfg.Addr, consts.ErrNotMaster)

		for name, tableInfo := range tables {
			if tableInfo.State != TableStateCKPTSyncDoing {
				continue
			}

			logger.Infof("aborting ongoing checkpoint sync for table %s", name)
			tableInfo.State = TableStateCKPTSyncFailed
			tableInfo.cancel(err)
		}
	}

	for {
		select {
		case <-ctx.Done():
			logger.Info("ckpt sync deamon routine stopped")
			return
		case <-ticker.C:
			tables := filterCkptSyncTables()
			if len(tables) == 0 {
				continue
			}

			isMaster, err := mgr.IsSourceClientMaster(ctx)

			if err != nil {
				logger.Errorf("failed to check if source client is master: %s", err)
				continue
			}

			if isMaster {
				logger.Debug("source client is master, no action needed")
				continue
			}

			// if source is not master, abort all ongoing checkpoint syncs
			logger.Infof("source <%s> is not master, aborting ongoing checkpoint syncs", mgr.sourceCfg.Addr)
			abortOngoingSyncs(tables)
		}
	}
}

func (mgr *R2SyncManager) ckptSyncRoutine(ctx context.Context, tableInfo *TableStatus, ckpt *CheckPoint) error {
	if tableInfo == nil {
		return nil
	}

	var err error

	ckptStatus := tableInfo.CkptStatus
	if ckptStatus == nil {
		err = utils.Errorf("CkptStatus is nil for table %s: %w", tableInfo.Name, consts.ErrInvalidArgument)
		logger.Error(err)
		return err
	}

	tableName := tableInfo.Name

	ckptStatus.StartTime = time.Now()
	defer func() {
		if err == nil {
			return
		}

		logger.Errorf("checkpoint sync for table %s failed: %s", tableName, err)
		tableInfo.MarkCkptFail(err.Error())
	}()

	dispatcher, err := mgr.ckptDispatcherFactory(ctx, tableInfo.Name, tableInfo.limiter)
	if err != nil {
		logger.Errorf("failed to create write dispatcher for table %s: %s", tableInfo.Name, err)
		return err
	}

	hlcConverter := pseudohlc.GetHlcConverter(tableName, -1)

	decoderFactory := func(slot int32) (*checkpointdecoder.CheckpointDecoder, error) {
		seq := uint64(0)
		decoder, err := checkpointdecoder.NewCheckpointDecoder(ckpt, mgr.useQueue, slot, mgr.serverID, seq, mgr.ckptBatchSize, tableInfo.Name, hlcConverter)
		return decoder, err
	}

	for !tableInfo.IsCkptPendingSlotEmpty() {
		slot, err := ckptStatus.PendingSlotRanges.GetFirstSlot()
		if err != nil {
			return err
		}

		if err = ctx.Err(); err != nil {
			logger.Errorf("context cancelled while syncing checkpoint for table %s, slot %d: %s", tableInfo.Name, slot, err)
			return err
		}

		decoder, err := decoderFactory(int32(slot))
		if err != nil {
			err = utils.Errorf("failed to create checkpoint decoder for table %s, slot %d: %w", tableInfo.Name, slot, err)
			return err
		}

		if err = mgr.ckptSyncOneSlot(ctx, decoder, dispatcher); err != nil {
			logger.Errorf("failed to sync checkpoint for table %s, slot %d: %s", tableInfo.Name, slot, err)
			if errors.Is(err, consts.ErrNotMaster) {
				logger.Infof("source <%s> is not master, aborting checkpoint sync for table %s", mgr.sourceCfg.Addr, tableInfo.Name)
				return err
			}
			if !errors.Is(err, consts.ErrNotMaster) {
				logger.Errorf("failed to sync checkpoint for table %s, slot %d, err: %s, retrying", tableInfo.Name, slot, err)
				time.Sleep(gCkptSyncRetryIntervalSec * time.Second)
				continue
			}
			logger.Infof("ckpt sync for table %s, slot %d stopped by user", tableInfo.Name, slot)
			return err
		}

		tableInfo.MarkCkptSlotDone(slot)
	}

	return nil
}

func (mgr *R2SyncManager) ckptSyncOneSlot(ctx context.Context, ckptDecoder *checkpointdecoder.CheckpointDecoder, dispatcher r2writer.StreamWriter) error {
	entryChan := make(chan *entry.Entry, gEntryChanSize)

	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		defer close(entryChan)
		return ckptDecoder.StartDecode(ctx, entryChan)
	})
	eg.Go(func() error {
		return dispatcher.Start(ctx, entryChan)
	})

	return eg.Wait()
}

func (mgr *R2SyncManager) StartCkptSync(ctx context.Context, tableName string, targetSlots SlotRanges) error {
	if targetSlots.Count() == 0 {
		return utils.Errorf("target slot ranges cannot be empty for table %s: %w", tableName, consts.ErrInvalidArgument)
	}

	if isMaster, err := mgr.IsSourceClientMaster(ctx); err != nil {
		return utils.Errorf("failed to check if source client is master: %w", err)
	} else if !isMaster {
		return consts.ErrNotMaster
	}

	var err error
	var tableStatus *TableStatus
	var inserted bool

	defer func() {
		if err != nil {
			logger.Errorf("failed to start checkpoint sync for table %s: %s", tableName, err)
		}
		if !inserted || err == nil {
			return
		}
		mgr.tableMutex.Lock()
		defer mgr.tableMutex.Unlock()
		delete(mgr.tables, tableName)
	}()

	mgr.tableMutex.Lock()
	tableStatus = mgr.tables[tableName]
	if tableStatus == nil {
		tableStatus = mgr.makeTableStatus(tableName)
		mgr.tables[tableName] = tableStatus
		inserted = true
	}
	mgr.tableMutex.Unlock()

	tableStatus.Lock()
	defer tableStatus.Unlock()

	if IsTableStateDoing(tableStatus.State) {
		err = utils.Errorf("table %s sync already exists, current state: %s", tableName, tableStatus.State)
		logger.Warn(err)
		return err
	}

	var ckptPath string
	ckptPath, err = mgr.createCkpt(ctx, tableName)
	if err != nil {
		return err
	}

	logger.Infof("created checkpoint for table %s at %s, target slots: %s", tableName, ckptPath, targetSlots)
	tableStatus.State = TableStateCKPTSyncDoing

	var ckpt *CheckPoint
	ckpt, err = mgr.getCkpt(tableName)
	if err != nil {
		return err
	}

	seq := ckpt.GetLatestSequenceNumber()

	ckptStatus := &CkptSyncStatus{
		TargetSlotRanges:  targetSlots,
		PendingSlotRanges: targetSlots,
		Seq:               seq,
	}

	tableStatus.CkptStatus = ckptStatus

	routineCtx, cancel := context.WithCancelCause(mgr.ctx)
	tableStatus.cancel = cancel

	ckptSyncRoutineWrapper := func() {
		defer cancel(nil)
		tableStatus.MarkCkptSyncStart()

		for {
			err := mgr.ckptSyncRoutine(routineCtx, tableStatus, ckpt)
			if err == nil {
				break
			}
			if errors.Is(err, consts.ErrNotMaster) {
				return
			}
			if utils.IsCanceled(routineCtx) != nil {
				logger.Infof("checkpoint sync for table %s is canceled by upper layer context", tableName)
				return
			}

			if errors.Is(err, consts.ErrStoppedByUser) {
				return
			}

			logger.Errorf("failed to sync checkpoint for table %s, retrying in %d seconds: %s", tableName, gCkptSyncRetryIntervalSec, err)
			time.Sleep(time.Duration(gCkptSyncRetryIntervalSec) * time.Second)
		}

		nextSeq := seq + 1
		logger.Infof("checkpoint sync for table %s completed successfully, starting PSYNC, with seq(%d)+1 -> %d", tableName, seq, nextSeq)
		go mgr.startPsync(tableName, nextSeq, tableStatus)
		mgr.removeCkpt(tableName)
	}

	go ckptSyncRoutineWrapper()
	return nil
}
