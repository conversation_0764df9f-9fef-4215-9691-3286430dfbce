package r2mgr

import (
	"os"
	"path"
	"sync"

	r2config "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_config"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/pelletier/go-toml/v2"
	"golang.org/x/time/rate"
)

const BurstSize = 16 << 20 // 16 MiB
const DefaultRateLimitMiBPerSec = 80
const RateLimitConfigFile = "ratelimit.toml"

type RateLimitConfigMgr struct {
	baseDir string
	mu      sync.Mutex

	// table_name -> r2config.RateLimitConfig
	config  map[string]r2config.RateLimitConfig
	limiter map[string]*rate.Limiter
}

func NewRateLimitConfigMgr(baseDir string) (*RateLimitConfigMgr, error) {
	cfgPath := path.Join(baseDir, RateLimitConfigFile)
	// load from file
	cfg := map[string]r2config.RateLimitConfig{}
	fileReader, err := os.Open(cfgPath)

	if err != nil && !os.IsNotExist(err) {
		return nil, err
	}

	if os.IsNotExist(err) {

		logger.Infof("rate limit config file %s not found, using default rate limit", cfgPath)
		err := os.MkdirAll(baseDir, 0755)
		if err != nil {
			err = utils.Errorf("failed to create directory %s: %w", baseDir, err)
			return nil, err
		}

	} else {

		if fileReader != nil {
			defer fileReader.Close()
		}

		err = toml.NewDecoder(fileReader).Decode(&cfg)
		if err != nil {
			return nil, err
		}
	}

	return &RateLimitConfigMgr{
		baseDir: baseDir,
		config:  cfg,
		mu:      sync.Mutex{},
		limiter: make(map[string]*rate.Limiter),
	}, nil
}

// retrun bytes per second
func (mgr *RateLimitConfigMgr) GetRateLimit(tableName string, createIfNotExists bool) (int64, *rate.Limiter) {
	mgr.mu.Lock()
	defer mgr.mu.Unlock()

	mib2Bytes := func(mib int64) int64 {
		return mib << 20
	}

	limiter, ok := mgr.limiter[tableName]
	if ok {
		return mib2Bytes(mgr.config[tableName].MiBPerSec), limiter
	}

	if !createIfNotExists {
		// if not create, return nil
		return 0, nil
	}

	// create a new limiter if not exists
	rlCfg, ok := mgr.config[tableName]
	if !ok {
		// use default rate limit if not configured
		rlCfg = r2config.RateLimitConfig{MiBPerSec: DefaultRateLimitMiBPerSec}
	}

	limiter = rate.NewLimiter(rate.Limit(mib2Bytes(rlCfg.MiBPerSec)), BurstSize)
	mgr.limiter[tableName] = limiter
	mgr.config[tableName] = rlCfg

	return mib2Bytes(rlCfg.MiBPerSec), limiter
}

func (mgr *RateLimitConfigMgr) SetRateLimit(tableName string, mibPerSec int64) {
	oldLimit, limiter := mgr.GetRateLimit(tableName, true)

	if oldLimit == mibPerSec {
		return
	}

	if mibPerSec <= 0 {
		mibPerSec = DefaultRateLimitMiBPerSec
		logger.Warnf("set rate limit for table %s to %d MiB/s, using default value", tableName, mibPerSec)
	}

	mgr.mu.Lock()
	defer mgr.mu.Unlock()

	limiter.SetLimit(rate.Limit(mibPerSec << 20))
	mgr.config[tableName] = r2config.RateLimitConfig{MiBPerSec: mibPerSec}
	logger.Infof("set rate limit for table %s to %d MiB/s", tableName, mibPerSec)

	tomlText, err := toml.Marshal(mgr.config)
	if err != nil {
		logger.Errorf("failed to marshal rate limit config: %s", err)
		return
	}

	save2File := func() {
		cfgPath := path.Join(mgr.baseDir, RateLimitConfigFile)
		err := utils.WriteFileAtomic(cfgPath, tomlText)
		if err == nil {
			return
		}
		logger.Errorf("failed to save rate limit config to file %s: %s", cfgPath, err)
	}
	go save2File()
}
