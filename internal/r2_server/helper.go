package r2server

import (
	"errors"
	"net/http"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"github.com/gin-gonic/gin"
)

type Response[T any] struct {
	Error string `json:"errmsg,omitempty"`
	Data  T      `json:"data,omitempty"`
}

func ResponseError(c *gin.Context, err error) {
	code := http.StatusInternalServerError
	if consts.IsNotFound(err) {
		code = http.StatusNotFound
	} else if errors.Is(err, consts.ErrIndexOutOfRange) {
		code = http.StatusBadRequest
	} else if errors.Is(err, consts.ErrAlreadyExists) {
		code = http.StatusConflict
	} else if errors.Is(err, consts.ErrForbidden) {
		code = http.StatusForbidden
	} else if errors.Is(err, consts.ErrInvalidArgument) {
		code = http.StatusBadRequest
	}

	c.<PERSON><PERSON>(code, Response[any]{
		Error: err.<PERSON><PERSON>r(),
		Data:  nil,
	})

	c.Abort()
}

func ResponseCreated(c *gin.Context) {
	c.JSON(http.StatusCreated, Response[any]{
		Error: "",
		Data:  nil,
	})
}

func ResponseOK[T any](c *gin.Context, data T) {
	c.JSON(http.StatusOK, Response[T]{
		Error: "",
		Data:  data,
	})
}
