package r2server

import (
	"fmt"
	"net/http/pprof"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func PProf(c *gin.Context) {
	switch c.Param("profile") {
	case "/cmdline":
		pprof.Cmdline(c.Writer, c.Request)
	case "/symbol":
		pprof.Symbol(c.Writer, c.Request)
	case "/profile":
		pprof.Profile(c.Writer, c.Request)
	case "/trace":
		pprof.Trace(c.Writer, c.Request)
	default:
		pprof.Index(c.Writer, c.Request)
	}
}

func (srv *Server) initHandlers() {
	engine := srv.engine
	engine.Any("/debug/pprof/*profile", PProf)
	engine.GET("/metrics", gin.WrapH(promhttp.Handler()))

	engine.NoRoute(func(c *gin.Context) {
		err := fmt.Errorf("path '%s' not found: %w", c.Request.URL.Path, consts.ErrNotFound)
		ResponseError(c, err)
	})

	handler := NewHandler(srv)

	apiV1 := engine.Group("/api/v1/")

	// table
	apiV1.GET("/table/:tableName", handler.GetTable)
	apiV1.POST("/table", handler.AddTableSync)
	apiV1.POST("/table/:tableName/stop", handler.StopTable)
	// rate limit
	apiV1.GET("/rate_limit/:tableName", handler.GetRateLimit)
	apiV1.POST("/rate_limit", handler.SetRateLimit)

}
