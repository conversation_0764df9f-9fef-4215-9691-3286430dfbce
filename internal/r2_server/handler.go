package r2server

import (
	"fmt"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/slotrange"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/gin-gonic/gin"
)

type Handler struct {
	srv *Server
}

func NewHandler(srv *Server) *Handler {
	return &Handler{
		srv: srv,
	}
}

// @Summary: List all tables
// @Router /table [get]
func (h *Handler) ListTables(c *gin.Context) {
	infos, err := h.srv.syncMgr.GetAllTables()
	if err != nil {
		ResponseError(c, err)
		return
	}
	ResponseOK(c, infos)
}

// @Summary: Get a specific table's information
// @Router /table/{tableName} [get]
func (h *Handler) GetTable(c *gin.Context) {
	info, err := h.srv.syncMgr.GetTableInfo(c.Param("tableName"))
	if err != nil {
		ResponseError(c, err)
		return
	}
	if info == nil {
		ResponseError(c, consts.ErrNotFound)
		return
	}
	ResponseOK(c, info)
}

type AddTableRequest struct {
	TableName  string               `json:"table_name" binding:"required"`
	Ckpt       bool                 `json:"ckpt"`        // Whether to sync checkpoint
	SlotRanges slotrange.SlotRanges `json:"slot_ranges"` // Optional slot ranges for checkpoint sync
}

// @Summary: Add a new table synchronization
// @Router /table [post]
// @Param body body AddTableRequest true "Request body"
func (h *Handler) AddTableSync(c *gin.Context) {
	var req AddTableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		err = utils.Errorf("%w: invalid request body: %s", consts.ErrInvalidArgument, err.Error())
		ResponseError(c, err)
		return
	}
	var err error

	if req.Ckpt {
		err = h.srv.syncMgr.StartCkptSync(c, req.TableName, req.SlotRanges)
	} else {
		err = h.srv.syncMgr.AddPsync(c, req.TableName)
	}

	if err != nil {
		ResponseError(c, err)
		return
	}
	ResponseCreated(c)
}

// @Summary: Stop a table's synchronization
// @Router /table/{tableName}/stop [post]
// @Param tableName path string true "Table name"
func (h *Handler) StopTable(c *gin.Context) {
	tableName := c.Param("tableName")
	if tableName == "" {
		ResponseError(c, consts.ErrInvalidArgument)
		return
	}

	err := h.srv.syncMgr.StopTable(tableName)
	if err != nil {
		ResponseError(c, err)
		return
	}
	ResponseOK(c, "Table synchronization stopped successfully")
}

type RateLimitResp struct {
	TableName string `json:"table_name"`
	MibPerSec int64  `json:"mib_per_sec"`
}

// @Summary: Get the rate limiter for a specific table
// @Router /rate_limit/{tableName} [get]
func (h *Handler) GetRateLimit(c *gin.Context) {
	bytesPerSec, limiter := h.srv.rateLimit.GetRateLimit(c.Param("tableName"), false)
	if limiter == nil {
		ResponseError(c, consts.ErrNotFound)
		return
	}
	resp := RateLimitResp{
		TableName: c.Param("tableName"),
		MibPerSec: bytesPerSec >> 20, // Convert bytes to MiB
	}
	ResponseOK(c, resp)
}

type SetRateLimitRequest struct {
	TableName string `json:"table_name"`
	MibPerSec int64  `json:"mib_per_sec"`
}

// @Summary: Set the rate limiter for a specific table
// @Router /rate_limit [post]
// @Param body body SetRateLimitRequest true "Request body"
func (h *Handler) SetRateLimit(c *gin.Context) {
	var req SetRateLimitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ResponseError(c, consts.ErrInvalidArgument)
		return
	}

	if req.MibPerSec <= 0 {
		err := fmt.Errorf("%w: invalid MibPerSec value: %d", consts.ErrInvalidArgument, req.MibPerSec)
		ResponseError(c, err)
		return
	}

	h.srv.rateLimit.SetRateLimit(req.TableName, req.MibPerSec)
	ResponseOK(c, "Rate limit set successfully")
}
