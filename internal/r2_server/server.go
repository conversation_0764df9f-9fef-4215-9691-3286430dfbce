package r2server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	r2config "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_config"
	r2mgr "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_mgr"
	"github.com/gin-gonic/gin"
)

type Server struct {
	conf      r2config.Config
	rateLimit *r2mgr.RateLimitConfigMgr
	memLimit  *r2mgr.MemMgrLimiter
	syncMgr   *r2mgr.R2SyncManager

	engine     *gin.Engine
	httpServer *http.Server
}

func NewServer(conf r2config.Config, syncMgr *r2mgr.R2SyncManager, rateLimit *r2mgr.RateLimitConfigMgr, memLimit *r2mgr.MemMgrLimiter) (*Server, error) {
	engine := gin.New()
	engine.Use(gin.Recovery())

	srv := &Server{
		conf:      conf,
		rateLimit: rateLimit,
		memLimit:  memLimit,
		syncMgr:   syncMgr,
		engine:    engine,
	}

	return srv, nil
}

func (srv *Server) StartAPIServer() {
	srv.initHandlers()
	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", srv.conf.General.ServePort),
		Handler: srv.engine,
	}

	go func() {
		if err := httpServer.ListenAndServe(); err != nil {
			if err == http.ErrServerClosed {
				return
			}
			panic(fmt.Errorf("API server: %w", err))
		}
	}()
	srv.httpServer = httpServer
}

func (srv *Server) Stop() error {
	gracefulCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	return srv.httpServer.Shutdown(gracefulCtx)
}
