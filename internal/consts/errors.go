package consts

import (
	"errors"
	"os"
)

var (
	ErrSlotOutOfRange    = errors.New("slot out of range")
	ErrNotFound          = errors.New("not found")
	ErrNotCmdHint        = errors.New("not cmd hint")
	ErrMissingTimestamp  = errors.New("missing timestamp")
	ErrNotMaster         = errors.New("not master")
	ErrForbidden         = errors.New("forbidden")
	ErrAlreadyExists     = errors.New("already exists")
	ErrIndexOutOfRange   = errors.New("index out of range")
	ErrInvalidArgument   = errors.New("invalid argument")
	ErrStoppedByUser     = errors.New("stopped by user")
	ErrIgnoredWriteBatch = errors.New("ignored write batch")
)

func IsNotFound(err error) bool {
	return errors.Is(err, ErrNotFound) || os.IsNotExist(err)
}
