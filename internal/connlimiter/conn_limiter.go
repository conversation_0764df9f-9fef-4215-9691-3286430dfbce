package connlimiter

import (
	"context"
	"net"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"go.uber.org/atomic"
	"golang.org/x/time/rate"
)

type rateLimitedConn struct {
	conn         net.Conn
	readLimiter  *rate.Limiter
	writeLimiter *rate.Limiter
	lastRead     int

	accumRead  func(n int)
	accumWrite func(n int)
}

func NewRateLimitedConn(conn net.Conn, readLimiter, writeLimiter *rate.Limiter, readBytes *atomic.Int64, writtenBytes *atomic.Int64) (*rateLimitedConn, error) {
	if limited, ok := conn.(*rateLimitedConn); ok {
		return limited, utils.Errorf("connection already rate-limited: %T", conn)
	}

	lastRead := 1024 // Default to 1KB for initial read size
	if readLimiter != nil {
		lastRead = readLimiter.Burst()
	}

	rConn := &rateLimitedConn{
		conn:         conn,
		readLimiter:  readLimiter,
		writeLimiter: writeLimiter,
		lastRead:     lastRead,
	}

	if readBytes != nil {
		rConn.accumRead = func(n int) {
			readBytes.Add(int64(n))
		}
	} else {
		rConn.accumRead = func(n int) {}
	}

	if writtenBytes != nil {
		rConn.accumWrite = func(n int) {
			writtenBytes.Add(int64(n))
		}
	} else {
		rConn.accumWrite = func(n int) {}
	}

	return rConn, nil
}

func (c *rateLimitedConn) Read(b []byte) (int, error) {
	if c.readLimiter == nil {
		return c.conn.Read(b)
	}

	ctx := context.Background()
	total := 0

	defer func() {
		c.accumRead(total)
	}()

	for total < len(b) {
		remaining := len(b) - total
		chunkSize := min(remaining, max(c.lastRead, 1), c.readLimiter.Burst())

		// probe non-blocking
		if err := c.readLimiter.Wait(ctx); err != nil {
			return total, err
		}

		probeSize := min(chunkSize, 1024)
		n, err := c.conn.Read(b[total : total+probeSize])
		actualRead := n
		total += actualRead

		if err != nil {
			if actualRead < 1 {
				c.readLimiter.Reserve().Cancel()
			}
			return total, err
		}

		if actualRead > 1 {
			if err := c.readLimiter.WaitN(ctx, actualRead-1); err != nil {
				c.readLimiter.ReserveN(time.Now(), -(actualRead - 1))
				return total, err
			}
		}

		c.lastRead = min(actualRead*2, c.readLimiter.Burst())
		if actualRead == 0 {
			// EOF reached, return total read so far
			return total, nil
		}
	}
	return total, nil
}

func (c *rateLimitedConn) Write(b []byte) (int, error) {
	if c.writeLimiter == nil {
		return c.conn.Write(b)
	}

	total := 0

	defer func() {
		c.accumWrite(total)
	}()

	for total < len(b) {
		remaining := len(b) - total
		chunkSize := min(remaining, c.writeLimiter.Burst())

		if err := c.writeLimiter.WaitN(context.Background(), chunkSize); err != nil {
			return total, err
		}

		bytesWritten, writeErr := c.conn.Write(b[total : total+chunkSize])
		actualUsed := bytesWritten

		if unused := chunkSize - actualUsed; unused > 0 {
			c.writeLimiter.ReserveN(time.Now(), -unused)
		}

		total += actualUsed

		if writeErr != nil {
			return total, writeErr
		}

		if bytesWritten == 0 {
			// EOF
			break
		}
	}
	return total, nil
}

func (c *rateLimitedConn) Close() error                       { return c.conn.Close() }
func (c *rateLimitedConn) LocalAddr() net.Addr                { return c.conn.LocalAddr() }
func (c *rateLimitedConn) RemoteAddr() net.Addr               { return c.conn.RemoteAddr() }
func (c *rateLimitedConn) SetDeadline(t time.Time) error      { return c.conn.SetDeadline(t) }
func (c *rateLimitedConn) SetReadDeadline(t time.Time) error  { return c.conn.SetReadDeadline(t) }
func (c *rateLimitedConn) SetWriteDeadline(t time.Time) error { return c.conn.SetWriteDeadline(t) }
