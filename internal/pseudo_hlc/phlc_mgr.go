package pseudohlc

import (
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type HlcConverter func(t time.Time) HLCClock

var phlcMap = utils.Map[string, *PseudoHLC]{}

func GetHlcConverter(id string, maxBackwardSec int) (converter HlcConverter) {
	makeConverter := func(phlc *PseudoHLC) HlcConverter {
		return func(t time.Time) HLCClock {
			return phlc.GetTimestamp(t)
		}
	}

	if phlc, ok := phlcMap.Load(id); ok {
		if maxBackwardSec > 0 {
			phlc.updateMaxBackwardSec(maxBackwardSec)
		}
		return makeConverter(phlc)
	}

	phlc := NewPseudoHLC(id, maxBackwardSec)
	phlcMap.LoadOrStore(id, phlc)

	// just retry
	return GetHlcConverter(id, maxBackwardSec)
}
