package pseudohlc

import (
	"fmt"
	"sync"
	"time"
)

type H<PERSON><PERSON>lock uint64

// Pseudo-HLC implementation (NOT true HLC algorithm).
// Features:
// - 48-bit physical time in 100ms units (higher bits)
// - 16-bit logical counter (lower bits)
// - Limited backward compatibility (5-second window)
// - No monotonic guarantee, only uniqueness (depends on the input time)
type PseudoHLC struct {
	id          string
	maxPhysical uint64 // Max observed physical time (100ms units)
	minPhysical uint64 // prevent psync's ts less then checkpoint's
	counters    map[uint64]uint32
	// negative value disables backward time limitations
	maxBackwardSec int

	sync.Mutex
}

func NewPseudoHLC(tableName string, maxBackwardSec int) *PseudoHLC {
	return &PseudoHLC{
		id:             tableName,
		maxPhysical:    0,
		counters:       make(map[uint64]uint32),
		maxBackwardSec: maxBackwardSec,
	}
}

func (h *PseudoHLC) GetID() string {
	return h.id
}

func (h *PseudoHLC) updateMaxBackwardSec(newMax int) {
	if newMax < 0 {
		return
	}

	h.Lock()
	defer h.Unlock()
	h.maxBackwardSec = max(newMax, 0)
	h.minPhysical = h.maxPhysical
}

func (h *PseudoHLC) String() string {
	if h == nil {
		return "<nil PseudoHLC>"
	}
	h.Lock()
	defer h.Unlock()
	return fmt.Sprintf("PseudoHLC{id: %s, maxPhysical: %d, counters: %v}", h.id, h.maxPhysical, h.counters)
}

func (h *PseudoHLC) GetTimestamp(t time.Time) HLCClock {
	return h.GetTimestamps(t, 1)[0]
}

func (h *PseudoHLC) GetTimestamps(t time.Time, n int) []HLCClock {
	if n <= 0 {
		panic("n must be greater than 0")
	}

	h.Lock()
	defer h.Unlock()

	results := make([]HLCClock, 0, n)

	// 10 units per sec
	physical := max(uint64(t.UnixNano()/1e8), h.minPhysical)

	originalPhysical := physical

	if physical > h.maxPhysical {
		h.maxPhysical = physical
		h.cleanup()
	} else if h.maxBackwardSec > 0 && physical < h.maxPhysical-uint64(h.maxBackwardSec)*10 {
		panic("Clock regression exceeds pseudo-HLC tolerance")
	}

	currentPhysical := originalPhysical
	remaining := n
	for remaining > 0 {

		counter, exists := h.counters[currentPhysical]
		if !exists {
			counter = 0
			h.counters[currentPhysical] = counter
		}

		available := 0xFFFF - int(counter)
		if available <= 0 {
			// current unit is exhausted, move to next
			currentPhysical++
			if currentPhysical > h.maxPhysical {
				h.maxPhysical = currentPhysical
				h.cleanup()
			}
			continue
		}

		batchSize := min(available, remaining)

		for i := 1; i <= batchSize; i++ {
			result := HLCClock((currentPhysical << 16) | uint64(counter+uint32(i)))
			results = append(results, result)
		}

		h.counters[currentPhysical] = counter + uint32(batchSize)
		remaining -= batchSize

		if remaining > 0 {
			currentPhysical++
			if currentPhysical > h.maxPhysical {
				h.maxPhysical = currentPhysical
				h.cleanup()
			}
		}
	}

	return results
}

func (h *PseudoHLC) cleanup() {
	if h.maxBackwardSec <= 0 {
		return
	}
	minPhysical := h.maxPhysical - (uint64(h.maxBackwardSec) * 10) // 100ms units
	for p := range h.counters {
		if p < minPhysical {
			delete(h.counters, p)
		}
	}
}
