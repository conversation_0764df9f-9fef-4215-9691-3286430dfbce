package pseudohlc

import (
	"testing"
	"time"
)

func TestPseudoHLC_Uniqueness(t *testing.T) {
	hlc := NewPseudoHLC("test", -1)
	fixedTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	n := 0xFFFF * 2 * 10

	timestamps := hlc.GetTimestamps(fixedTime, n)
	seen := make(map[HLCClock]bool)

	for _, ts := range timestamps {
		if seen[ts] {
			t.Fatalf("Duplicate timestamp: %v", ts)
		}
		seen[ts] = true
	}
}

func TestPseudoHLC_MonoIncre(t *testing.T) {
	hlc := NewPseudoHLC("test", -1)
	fixedTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	n := 0xFFFF * 2 * 10

	prev := HLCClock(0)
	for range n {
		ts := hlc.GetTimestamp(fixedTime)
		if ts <= prev {
			t.Fatalf("Non-monotonic timestamp: %v (prev: %v)", ts, prev)
		}

		prev = ts
	}

}
