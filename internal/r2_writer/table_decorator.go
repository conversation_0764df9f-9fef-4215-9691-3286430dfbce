package r2writer

// type TableWriter struct {
// 	tableName string
// 	writer    Writer
// }

// // Ensure TableWriter implements Writer interface
// var _ Writer = (*TableWriter)(nil)

// func NewTableWriter(tableName string, writer Writer) *TableWriter {
// 	return &TableWriter{
// 		tableName: tableName,
// 		writer:    writer,
// 	}
// }

// func (tw *TableWriter) WriteEntries(ctx context.Context, entries []*entry.Entry) error {
// 	for _, entry := range entries {
// 		for _, cmd := range entry.Cmds {
// 			if len(cmd.Tokens) < 2 {
// 				continue
// 			}
// 			oldKey := ""
// 			switch key := cmd.Tokens[cmd.KeyPos].(type) {
// 			case string:
// 				oldKey = key
// 			case []byte:
// 				oldKey = string(key)
// 			default:
// 				return utils.Errorf("unsupported key type: %T", cmd.Tokens[cmd.KeyPos])
// 			}
// 			newKey := tw.tableName + ":" + oldKey
// 			cmd.Tokens[cmd.KeyPos] = newKey
// 		}
// 	}
// 	err := tw.writer.WriteEntries(ctx, entries)
// 	return err
// }

// func (tw *TableWriter) Close() {
// 	tw.writer.Close()
// }
