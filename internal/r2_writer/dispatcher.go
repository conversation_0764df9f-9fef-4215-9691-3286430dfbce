package r2writer

import (
	"context"
	"sync"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

var _ StreamWriter = (*SimpleStremWriter)(nil)
var _ StreamWriter = (*AccumuStreamWriter)(nil)

type SimpleStremWriter struct {
	writer   Writer
	parallel int
}

func NewSimpleDispatcher(writer Writer, parallel int) *SimpleStremWriter {
	return &SimpleStremWriter{
		writer:   writer,
		parallel: parallel,
	}
}

func (d *SimpleStremWriter) Start(ctx context.Context, entrySource <-chan *entry.Entry) error {
	if entrySource == nil {
		panic("entrySource cannot be nil")
	}

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	var wg sync.WaitGroup
	var once sync.Once
	var firstErr error
	wg.Add(d.parallel)

	worker := func() {
		defer wg.Done()

		for {
			select {
			case <-ctx.Done():
				return
			case e, ok := <-entrySource:
				if !ok {
					return
				}
				if err := d.writer.WriteEntries(ctx, []*entry.Entry{e}); err != nil {
					once.Do(func() {
						firstErr = err
						cancel() // cancel the context to stop all workers
					})
					return
				}
			}
		}
	}

	for range d.parallel {
		go worker()
	}

	wg.Wait()
	return firstErr
}

func (d *SimpleStremWriter) Close() {
	if d.writer != nil {
		d.writer.Close()
	}
}

type AccumuStreamWriter struct {
	writer        Writer
	interval      time.Duration
	batchSize     int
	pendingCmdCnt int

	cancel context.CancelCauseFunc
}

func NewAccumuStreamWriter(writer Writer, interval time.Duration, batchSize int) *AccumuStreamWriter {
	return &AccumuStreamWriter{
		writer:    writer,
		interval:  interval,
		batchSize: batchSize,
	}
}

func (d *AccumuStreamWriter) Start(ctx context.Context, entrySource <-chan *entry.Entry) error {
	if entrySource == nil {
		panic("entrySource cannot be nil")
	}

	ctx, d.cancel = context.WithCancelCause(ctx)
	defer d.cancel(nil)

	ticker := time.NewTicker(d.interval)
	defer ticker.Stop()

	batch := make([]*entry.Entry, 0, d.batchSize)
	d.pendingCmdCnt = 0

	var retErr error

	defer func() {
		if len(batch) == 0 {
			return
		}

		if _, err := d.flushBatch(ctx, batch); err != nil {
			logger.Errorf("failed to flush remaining batch entries: %s", err)
			if retErr == nil {
				retErr = err
			}
		}
	}()

	for retErr == nil {
		select {
		case <-ctx.Done():
			return nil

		case e, ok := <-entrySource:
			if !ok {
				return utils.Errorf("entry source closed")
			}

			batch = append(batch, e)
			d.pendingCmdCnt += len(e.Cmds)

			if d.pendingCmdCnt < d.batchSize {
				continue
			}

			batch, retErr = d.flushBatch(ctx, batch)

		case <-ticker.C:
			if len(batch) == 0 {
				continue
			}
			batch, retErr = d.flushBatch(ctx, batch)
		}
	}

	if retErr != nil {
		logger.Error(retErr)
	}

	return retErr
}

func (d *AccumuStreamWriter) flushBatch(ctx context.Context, batch []*entry.Entry) ([]*entry.Entry, error) {
	if len(batch) == 0 {
		return batch, nil
	}

	if ctx.Err() != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
		defer cancel()
		logger.Warnf("flushing entries after context cancelled, entries count: %d, this process may take at most 10 seconds", len(batch))
	}

	if err := d.writer.WriteEntries(ctx, batch); err != nil {
		logger.Errorf("failed to write batch entries: %s", err)
		return batch, err
	}

	d.pendingCmdCnt = 0

	// reuse the batch slice
	return batch[:0], nil
}

func (d *AccumuStreamWriter) Close() {
	if d.cancel != nil {
		d.cancel(nil)
	}
	if d.writer != nil {
		d.writer.Close()
	}
}
