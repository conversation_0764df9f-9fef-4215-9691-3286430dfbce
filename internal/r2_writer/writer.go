package r2writer

import (
	"context"
	"net"
	"sort"
	"strings"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/connlimiter"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/kvrockscontroller"
	r2config "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2_config"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"git.xiaojukeji.com/kvdb/go-redis/v8"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/atomic"
	"golang.org/x/time/rate"
)

var (
	// 写入操作耗时
	writeDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "redis_writer_write_duration_seconds",
			Help:    "Duration of Redis write operations",
			Buckets: []float64{.001, .005, .01, .05, .1, .5, 1, 2, 5},
		},
		[]string{"table"},
	)
	// 写入错误计数器
	writeErrors = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_writer_write_errors_total",
			Help: "Total number of write errors",
		},
		[]string{"table", "error_type"},
	)
	// 重试次数计数器
	retryCount = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_writer_retries_total",
			Help: "Total number of write retries",
		},
		[]string{"table"},
	)
	// 处理条目计数器
	entriesProcessed = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_writer_entries_processed_total",
			Help: "Total number of processed entries",
		},
		[]string{"table"},
	)
	// 处理命令计数器
	commandsProcessed = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_writer_commands_processed_total",
			Help: "Total number of processed commands",
		},
		[]string{"table"},
	)
	// Pipeline大小分布
	pipelineSize = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "redis_writer_pipeline_size",
			Help:    "Number of commands in pipeline executions",
			Buckets: []float64{10, 50, 100, 500, 1000},
		},
		[]string{"table"},
	)
	// Ping操作延迟
	pingLatency = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "redis_writer_ping_latency_seconds",
			Help:    "Latency of Redis ping operations",
			Buckets: []float64{.001, .005, .01, .05, .1, .5},
		},
		[]string{"table"},
	)
	// 发送流量计数器
	sentBytes = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_writer_sent_bytes_total",
			Help: "Total number of bytes sent to Redis",
		},
		[]string{"table"},
	)
)

func init() {
	// 注册所有指标
	prometheus.MustRegister(
		writeDuration,
		writeErrors,
		retryCount,
		entriesProcessed,
		commandsProcessed,
		pipelineSize,
		pingLatency,
	)
}

type RedisWriter struct {
	client          *redis.ClusterClient
	rateLimiter     *rate.Limiter
	maxRetries      int
	maxBatchSize    int32
	tableName       string
	bytesSent       *atomic.Int64
	commandExecuted *atomic.Int64
}

func NewRedisClusterWriter(ctx context.Context, tableName string, targetConfig *r2config.TargetConfig, rateLimiter *rate.Limiter, kvController *kvrockscontroller.Client) (*RedisWriter, error) {
	dialTimeout := time.Duration(targetConfig.Conn.DialTimeoutMs) * time.Millisecond
	readTimeout := time.Duration(targetConfig.Conn.ReadTimeoutMs) * time.Millisecond
	writeTimeout := time.Duration(targetConfig.Conn.WriteTimeoutMs) * time.Millisecond
	bytesSent := &atomic.Int64{}
	commandExecuted := &atomic.Int64{}

	limitConnDialer := func(ctx context.Context, network, addr string) (net.Conn, error) {
		netDialer := &net.Dialer{
			Timeout:   dialTimeout,
			KeepAlive: 5 * time.Minute,
		}

		// todo: support tls
		conn, err := netDialer.DialContext(ctx, network, addr)
		if err != nil {
			return nil, err
		}

		return connlimiter.NewRateLimitedConn(conn, nil, rateLimiter, nil, bytesSent)
	}

	addrs, err := kvController.GetAllAddrs(ctx)
	if err != nil {
		return nil, err
	}

	opt := &redis.ClusterOptions{
		ClusterSlots: kvController.GetRedisClusterSlots,
		Addrs:        addrs,
		MaxRedirects: targetConfig.MaxRedirectTimes,
		Dialer:       limitConnDialer,
		DialTimeout:  dialTimeout,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
		MaxRetries:   targetConfig.WriteRetry,
		PoolSize:     targetConfig.Parallel,
		Username:     targetConfig.Auth.Username,
		Password:     targetConfig.Auth.Username,
	}

	client := redis.NewClusterClient(opt)

	return &RedisWriter{
		client:          client,
		rateLimiter:     rateLimiter,
		maxRetries:      targetConfig.WriteRetry,
		maxBatchSize:    targetConfig.PipelineWriteBatch,
		tableName:       tableName,
		bytesSent:       bytesSent,
		commandExecuted: commandExecuted,
	}, nil
}

func (w *RedisWriter) WriteEntries(ctx context.Context, entries []*entry.Entry) error {
	if len(entries) == 0 {
		return nil
	}

	entriesProcessed.WithLabelValues(w.tableName).Add(float64(len(entries)))

	timer := prometheus.NewTimer(prometheus.ObserverFunc(func(v float64) {
		writeDuration.WithLabelValues(w.tableName).Observe(v)
	}))
	defer timer.ObserveDuration()
	lastBytesSent := w.bytesSent.Load()
	prefixSum := make([]int, len(entries)+1)
	for i := range entries {
		prefixSum[i+1] = prefixSum[i] + len(entries[i].Cmds)
	}
	commandsProcessed.WithLabelValues(w.tableName).Add(float64(prefixSum[len(prefixSum)-1]))
	var outErr error

	eStart, cStart := 0, 0
	for retry := 0; ; retry++ {
		nextE, nextC, cmdCount, err := w.execBatch(ctx, entries, eStart, cStart)
		for i := eStart; i < nextE; i++ {
			if entries[i].Consumed != nil {
				entries[i].Consumed()
			}
		}

		if err != nil {
			writeErrors.WithLabelValues(w.tableName, "write_error").Inc()

			failedPos := prefixSum[eStart] + cmdCount - 1
			failedEntryIndex := max(sort.Search(len(prefixSum), func(i int) bool {
				return prefixSum[i] > failedPos
			})-1, 0)

			if strings.Contains(err.Error(), "TRYAGAIN") {
				logger.Warnf("target redis is busy, err: %s", err)
				retry = 0 // Reset retry count on TRYAGAIN response; not treated as an error
				time.Sleep(500 * time.Millisecond)
			} else {
				if retry >= w.maxRetries {
					logger.Errorf("failed to write entry %d with %d commands after %d retries: %s", failedEntryIndex, cmdCount, retry, err)
					outErr = err
					break
				}
				logger.Warnf("retrying write entry %d with %d commands after %d retries: %s", failedEntryIndex, cmdCount, retry, err)
				time.Sleep(100 * time.Millisecond)
			}
			eStart, cStart = failedEntryIndex, 0
			continue
		}

		if nextE >= len(entries) {
			break
		}

		eStart, cStart = nextE, nextC
		retry = 0
	}

	sentBytes.WithLabelValues(w.tableName).Add(float64(w.bytesSent.Load() - lastBytesSent))
	return outErr
}

func (w *RedisWriter) execBatch(ctx context.Context, entries []*entry.Entry, eStart, cStart int) (nextE, nextC, cmdCount int, err error) {
	pipeline := w.client.Pipeline()
	defer pipeline.Close()

	curE, curC := eStart, cStart
	execed := 0

	for curE < len(entries) && execed < int(w.maxBatchSize) {
		e := entries[curE]
		if len(e.Cmds) == 0 {
			curE += 1
			curC = 0
			continue
		}

		avail := len(e.Cmds) - curC
		add := min(int(w.maxBatchSize)-execed, avail)

		for i := range add {
			cmd := &e.Cmds[curC+i]
			tokens := cmd.GetTokens(w.tableName)
			c := pipeline.Do(ctx, tokens...)
			logger.Debugf("Pipeline command: %s", c.String())
		}

		execed += add
		curC += add

		if curC >= len(e.Cmds) {
			curE += 1
			curC = 0
		}
	}

	pipelineSize.WithLabelValues(w.tableName).Observe(float64(execed))
	cmds, err := pipeline.Exec(ctx)

	if err == nil {
		if logger.IsDebugMode() {
			for i, cmd := range cmds {
				logger.Debugf("Executed %dth: %s", i, cmd.String())
			}
		}

		return curE, curC, execed, nil
	}

	for i, cmd := range cmds {
		if cmd.Err() != nil {
			err = utils.Errorf("Pipeline %d th command <%s> failed: %w", i, cmd.String(), cmd.Err())
			// i - 1 means that the previous command was successful
			return 0, 0, min(0, i-1), err
		}
	}

	panic("unreachable")
}

func (w *RedisWriter) Close() {
	if w.client != nil {
		w.client.Close()
	}
}
