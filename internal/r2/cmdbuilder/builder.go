package cmdbuilder

import (
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	pseudohlc "git.xiaojukeji.com/fusion/fusion-syncer/internal/pseudo_hlc"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
)

type HlcConverter = pseudohlc.HlcConverter

const (
	// when a int64 is converted to a string,
	// the string's max length is 20 characters.
	// so we use 20 as the segment size for numeric commands.
	gNumericCmdSegSize = 20
)

type CmdSeg struct {
	args     []any
	ts       time.Time
	dataSize int
}

type CmdBuilder struct {
	serverID     string
	seq          uint64
	cmd          string
	key          string
	args         []CmdSeg
	hlcConverter HlcConverter
}

func NewCmdBuilder(serverID string, seq uint64, cmd string, key string, converter HlcConverter) *CmdBuilder {
	return &CmdBuilder{
		serverID:     serverID,
		seq:          seq,
		cmd:          cmd,
		key:          key,
		args:         []CmdSeg{},
		hlcConverter: converter,
	}
}

type CmdBuilderBuilder func(cmd string, key string) *CmdBuilder

func NewCmdBuilderBuilder(serverID string, seq uint64, converter HlcConverter) CmdBuilderBuilder {
	return func(cmd string, key string) *CmdBuilder {
		defer func() { seq += 1 }()
		return NewCmdBuilder(serverID, seq, cmd, key, converter)
	}
}

func NewCmdSeg(ts time.Time, args ...any) CmdSeg {
	dataSize := 0
	for _, arg := range args {
		switch v := arg.(type) {
		case string:
			dataSize += len(v)
		case []byte:
			dataSize += len(v)
		case int, int64, uint, uint64:
			dataSize += gNumericCmdSegSize
		default:
			logger.Warnf("unsupported command argument type: %T, value: %v", v, v)
		}
	}
	return CmdSeg{
		args:     args,
		ts:       ts,
		dataSize: dataSize,
	}
}

func (cb *CmdBuilder) AddSegs(segs ...CmdSeg) *CmdBuilder {
	if len(segs) == 0 {
		panic("CmdBuilder.AddSegs: no segments to add")
	}
	cb.args = append(cb.args, segs...)
	return cb
}

func (cb *CmdBuilder) AddSeg(ts time.Time, args ...any) *CmdBuilder {
	seg := NewCmdSeg(ts, args...)
	return cb.AddSegs(seg)
}

func (cb *CmdBuilder) Build() entry.Command {
	preallocate := max(8, len(cb.args)*2+4) // cmd + key + pargs + serverID + seq
	tokens := make([]any, 0, preallocate)
	tokens = append(tokens, cb.cmd, cb.key)
	dataSize := 0

	for _, seg := range cb.args {
		tokens = append(tokens, seg.args...)
		hlc := cb.hlcConverter(seg.ts)
		tokens = append(tokens, uint64(hlc))
		dataSize += seg.dataSize
	}

	tokens = append(tokens, cb.serverID, cb.seq)

	return entry.Command{
		Tokens:   tokens,
		KeyPos:   1, // The first argument is the key
		DataSize: dataSize,
		Seq:      cb.seq,
	}
}

func (cb *CmdBuilder) Empty() bool {
	return len(cb.args) == 0
}
