package r2

import (
	"errors"
)

var (
	ErrInvalidR2Key = errors.New("invalid R2 key")
)

const (
	MIN_R2_SLOT = 0
	// R2 has 1024 slots, so the max slot is 1023
	MAX_R2_SLOT = 1023
)

var (
	SLOT_SPLITTER = []byte("$^$")
	KEY_SPLITTER  = []byte("&&")
	FUION_EMPTY   = []byte("fusion:empty:")
)

const (
	VALUE_TERMINATOR = '$'
	TTL_LEN          = 16
	SEC_TS_LEN       = 10
	US_TS_LEN        = 16
)

const (
	DEL_CMD          = "__del"
	HDEL_CMD         = "__hdel"
	PEXPIREAT_CMD    = "__pexpireat"
	PSETEXPIREAT_CMD = "__psetexat"
	SET_CMD          = "__set"
	LPUSH_CMD        = "__lpush"
	RPUSH_CMD        = "__rpush"
	LSET_CMD         = "__lset"
	LREM_CMD         = "__lrem"
	SADD_CMD         = "__sadd"
	SREM_CMD         = "__srem"
	HSET_CMD         = "__hset"
	ZADD_CMD         = "__zadd"
	ZREM_CMD         = "__zrem"
)
