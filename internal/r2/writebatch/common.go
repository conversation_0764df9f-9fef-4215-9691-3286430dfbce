package writebatch

import (
	"encoding/binary"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/samber/mo"
)

type TsOpt = mo.Option[time.Time]
type Bytes = []byte

var TS_PREFIX = []byte("timestamp:")
var LOG_CMD_PREFIX = []byte("log_cmd:")
var SYNC_FLAG = []byte("synced")
var REPL_FLAG = []byte("repl")

type OpType string

const (
	OpTypeDel   OpType = "optp_del"
	OpTypePut   OpType = "optp_put"
	OpTypeMerge OpType = "optp_merge"
)

func parseVarint32(data []byte) (res uint32, remaining []byte, err error) {
	isEmpty := func(d []byte) bool {
		return len(d) == 0
	}

	res = 0
	shift := uint32(0)
	cursor := data

	for !isEmpty(cursor) && shift <= 28 {
		b := cursor[0]
		cursor = cursor[1:]

		res |= (uint32(b&0x7F) << shift)
		shift += 7

		if b&0x80 == 0 {
			return res, cursor, nil
		}
	}

	if !isEmpty(cursor) && shift == 35 {
		b := cursor[0]
		cursor = cursor[1:]
		res |= (uint32(b&0x7F) << 28)
		return res, cursor, nil
	}

	return 0, nil, utils.Errorf("invalid varint32 encoding: %v", data)
}

type WriteBatchHintType string

const (
	WriteBatchHintTypeLpop    WriteBatchHintType = "wbh_lpop"
	WriteBatchHintTypeRpop    WriteBatchHintType = "wbh_rpop"
	WriteBatchHintTypeLpush   WriteBatchHintType = "wbh_lpush"
	WriteBatchHintTypeRpush   WriteBatchHintType = "wbh_rpush"
	WriteBatchHintTypeLtrim   WriteBatchHintType = "wbh_ltrim"
	WriteBatchHintTypeLsetNew WriteBatchHintType = "wbh_lset_new"
	WriteBatchHintTypeLsetOld WriteBatchHintType = "wbh_lset_old"
	WriteBatchHintTypeLrem    WriteBatchHintType = "wbh_lrem"
)

type LPopHint struct {
	Ts           time.Time
	RemovedValue Bytes
}

type RPopHint struct {
	Ts           time.Time
	RemovedValue Bytes
}

type LPushHint struct {
	Ts  time.Time
	Cnt int32
}

type RPushHint struct {
	Ts  time.Time
	Cnt int32
}

type LTrimHint struct {
	Ts time.Time
	// 1 means left; -1 means right
	Direction int

	RemovedValues []Bytes
}

type LSetNewHint struct {
}

type LSetOldHint struct {
	Index int32
}

type LRemHint struct {
	Ts           time.Time
	RemovedValue Bytes
	Cnt          int32
}

type WriteBatchHint interface {
	// LPopHint | RPopHint | LPushHint | RPushHint | LTrimHint | LSetNewHint | LSetOldHint | LRemHint

	Type() WriteBatchHintType
}

const TsLen = 8 // 8 bytes for microsecond timestamp

func parseTs(data Bytes) (ts time.Time, remaining Bytes, err error) {
	if len(data) < TsLen {
		return time.Time{}, nil, utils.Errorf("invalid timestamp length: %d, expected at least 8 bytes", len(data))
	}
	tsBytes := data[:TsLen]
	remaining = data[TsLen:]

	usCnt := binary.LittleEndian.Uint64(tsBytes)
	ts = time.UnixMicro(int64(usCnt))
	return
}
