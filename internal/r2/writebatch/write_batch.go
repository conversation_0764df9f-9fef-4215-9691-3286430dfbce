package writebatch

import (
	"bytes"
	"encoding/binary"

	"git.xiaojukeji.com/foundation/fusion/gorocksdb"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/samber/mo"
)

type WriteBatchEntry struct {
	CfID   uint32
	OpType OpType
	// valid if OpType in {del, put, merge}
	Key []byte
	// valid if OpType in {put, merge}
	Value []byte
	Seq   uint64
}

type WriteBatch struct {
	Seq         uint64
	Ts          TsOpt
	SyncFlag    bool
	ReplFlag    bool
	PersistFlag bool
	Entries     []WriteBatchEntry
	ListHints   []WriteBatchHint
}

func ParseWriteBatch(rawData []byte) (*WriteBatch, error) {
	dataLen := len(rawData)
	if dataLen < 12 {
		return nil, utils.Errorf("invalid write batch data length: %d", dataLen)
	}

	var err error
	data := rawData
	seq := binary.LittleEndian.Uint64(data[:8])
	data = data[8:]
	cnt := binary.LittleEndian.Uint32(data[:4])
	data = data[4:]

	wb := &WriteBatch{
		Seq:     seq,
		Entries: make([]WriteBatchEntry, 0, cnt),
	}

	wbEntrySeq := seq
	getEntrySeq := func() uint64 {
		curSeq := wbEntrySeq
		wbEntrySeq++
		return curSeq
	}

	for len(data) > 0 {
		var cfID uint32 = 0

		recordType := gorocksdb.WriteBatchRecordType(data[0])
		data = data[1:]

		switch recordType {
		case gorocksdb.WriteBatchCFDeletionRecord, gorocksdb.WriteBatchCFValueRecord, gorocksdb.WriteBatchCFMergeRecord:
			cfID, data, err = parseCfID(data)
			if err != nil {
				return nil, err
			}
		}

		switch recordType {
		case gorocksdb.WriteBatchDeletionRecord, gorocksdb.WriteBatchCFDeletionRecord:
			{
				var keyLen uint32 = 0
				keyLen, data, err = parseVarintWitLength(data)
				if err != nil {
					return nil, err
				}
				key := data[:keyLen]
				data = data[keyLen:]
				wb.Entries = append(wb.Entries, WriteBatchEntry{
					CfID:   cfID,
					OpType: OpTypeDel,
					Key:    key,
					Seq:    getEntrySeq(),
				})
			}
		case gorocksdb.WriteBatchValueRecord, gorocksdb.WriteBatchCFValueRecord:
			{
				var key, val []byte
				key, val, data, err = parseKv(data)
				if err != nil {
					return nil, err
				}
				wb.Entries = append(wb.Entries, WriteBatchEntry{
					CfID:   cfID,
					OpType: OpTypePut,
					Key:    key,
					Value:  val,
					Seq:    getEntrySeq(),
				})
			}
		case gorocksdb.WriteBatchMergeRecord, gorocksdb.WriteBatchCFMergeRecord:
			{
				var key, val []byte
				key, val, data, err = parseKv(data)
				if err != nil {
					return nil, err
				}
				wb.Entries = append(wb.Entries, WriteBatchEntry{
					CfID:   cfID,
					OpType: OpTypeMerge,
					Key:    key,
					Value:  val,
					Seq:    getEntrySeq(),
				})
			}
		case gorocksdb.WriteBatchLogDataRecord:
			{
				var valLen uint32 = 0
				var val []byte
				valLen, data, err = parseVarintWitLength(data)
				if err != nil {
					return nil, err
				}
				val = data[:valLen]
				err = parseLogData(val, wb)
				if err != nil {
					return nil, err
				}

				data = data[valLen:]
			}
		}
	}

	if wbEntrySeq-seq != uint64(len(wb.Entries)) {
		logger.Warnf("write batch entry count mismatch: expected %d, got %d", wbEntrySeq-seq, len(wb.Entries))
	}

	return wb, nil
}

func parseCfID(data []byte) (uint32, []byte, error) {
	return parseVarint32(data)
}

func parseVarintWitLength(data []byte) (uint32, []byte, error) {
	length, remaining, err := parseVarint32(data)
	if err != nil {
		return 0, nil, utils.Errorf("failed to parse varint with length: %w", err)
	}
	if length > uint32(len(remaining)) {
		return 0, nil, utils.Errorf("invalid varint length: %d, remaining data length: %d", length, len(remaining))
	}
	return length, remaining, nil
}

func parseKv(data []byte) (key, val []byte, remaining []byte, err error) {
	var keyLen uint32 = 0
	var valLen uint32 = 0

	keyLen, data, err = parseVarintWitLength(data)
	if err != nil {
		return
	}

	key = data[:keyLen]
	data = data[keyLen:]

	valLen, data, err = parseVarintWitLength(data)
	if err != nil {
		return
	}
	val = data[:valLen]
	data = data[valLen:]

	remaining = data
	return key, val, remaining, nil
}

func parseLogData(data []byte, wb *WriteBatch) error {
	if len(data) == 0 {
		return nil
	}

	switch data[0] {
	case 'r':
		wb.ReplFlag = bytes.HasPrefix(data, REPL_FLAG)
	case 's':
		wb.SyncFlag = bytes.HasPrefix(data, SYNC_FLAG)
	case 't':
		{
			if !bytes.HasPrefix(data, TS_PREFIX) {
				return utils.Errorf("invalid timestamp data: %s", data)
			}

			tsData := data[len(TS_PREFIX):]
			ts, err := parser.ParseSecTs(tsData)
			if err != nil {
				return err
			}
			wb.Ts = TsOpt(mo.Some(ts))
		}
	case 'l':
		{
			if bytes.Equal(data, []byte("log_cmd:persist")) {
				wb.PersistFlag = true
				return nil
			}

			wbHint, err := ParseWriteBatchHint(data)
			if err != nil {
				return err
			}
			wb.ListHints = append(wb.ListHints, wbHint)
		}
	default:
		return utils.Errorf("unknown log data prefix: %s", data[:1])
	}
	return nil
}
