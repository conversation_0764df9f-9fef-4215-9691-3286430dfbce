package writebatch

import (
	"bytes"
	"encoding/binary"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

func (*LPopHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeLpop
}

func (*RPopHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeRpop
}

func (*LPushHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeLpush
}

func (*RPushHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeRpush
}

func (*LTrimHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeLtrim
}

func (*LSetNewHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeLsetNew
}

func (*LSetOldHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeLsetOld
}

func (*LRemHint) Type() WriteBatchHintType {
	return WriteBatchHintTypeLrem
}

func ParseWriteBatchHint(rawData Bytes) (WriteBatchHint, error) {
	if !bytes.HasPrefix(rawData, LOG_CMD_PREFIX) {
		prefix := rawData[:min(len(rawData), len(LOG_CMD_PREFIX))]
		return nil, utils.Errorf("Invalid log_cmd, prefix is `%s`: %w", prefix, consts.ErrNotCmdHint)
	}

	var err error
	data := rawData[len(LOG_CMD_PREFIX):]
	splitted := bytes.SplitN(data, Bytes(":"), 2)
	if len(splitted) == 1 {
		if bytes.Equal(splitted[0], Bytes("lset")) {
			return &LSetNewHint{}, nil
		}
		return nil, utils.Errorf("Invalid log_cmd, no splitter found in `%s`: %w", splitted[0], consts.ErrNotCmdHint)
	}

	cmd, data := string(splitted[0]), splitted[1]
	switch cmd {
	case "lpop":
		var lpopHint LPopHint
		_, err = parsePop(data, &lpopHint.Ts, &lpopHint.RemovedValue)
		return &lpopHint, err
	case "rpop":
		var rpopHint RPopHint
		_, err = parsePop(data, &rpopHint.Ts, &rpopHint.RemovedValue)
		return &rpopHint, err
	case "lpush":
		var lpopHint LPushHint
		_, err = parsePush(data, &lpopHint.Ts, &lpopHint.Cnt)
		return &lpopHint, err
	case "rpush":
		var rpopHint RPushHint
		_, err = parsePush(data, &rpopHint.Ts, &rpopHint.Cnt)
		return &rpopHint, err
	case "ltrim":
		return parseTrim(data)
	case "lset":
		var lsetHint LSetOldHint
		if len(data) < 4 {
			return nil, utils.Errorf("invalid lset data length: %d, expected at least 4", len(data))
		}
		lsetHint.Index = int32(binary.LittleEndian.Uint32(data[:4]))
		return &lsetHint, nil
	case "lrem":
		return parseLrem(data)
	default:
		return nil, utils.Errorf("Invalid log_cmd `%s`: %w", cmd, consts.ErrNotCmdHint)
	}
}

func parseCnt(data Bytes) (cnt int32, remaining Bytes, err error) {
	if len(data) < 4 {
		return 0, nil, utils.Errorf("invalid count data length: %d, expected at least 4", len(data))
	}

	res := int32(binary.LittleEndian.Uint32(data[:4]))
	remaining = data[4:]

	return res, remaining, nil
}

func parseBytes(data Bytes, retValue *Bytes) (remaining Bytes, err error) {
	if len(data) < 4 {
		return nil, utils.Errorf("invalid bytes data length: %d, expected at least 4", len(data))
	}
	length := binary.LittleEndian.Uint32(data[:4])
	if len(data) < int(length)+4 {
		return nil, utils.Errorf("invalid bytes data length: %d, expected at least %d", len(data), length+4)
	}
	*retValue = data[4 : 4+length]
	return data[4+length:], nil
}

func parsePop(data Bytes, retTs *time.Time, retValue *Bytes) (remaining Bytes, err error) {
	if len(data) < TsLen+4 {
		return nil, utils.Errorf("invalid pop data length: %d, expected at least %d", len(data), TsLen+4)
	}

	var tmpTs time.Time
	var tmpValue Bytes

	tmpTs, remaining, err = parseTs(data)
	if err != nil {
		return nil, utils.Errorf("failed to parse pop timestamp: %w", err)
	}

	remaining, err = parseBytes(remaining, &tmpValue)
	if err != nil {
		return nil, utils.Errorf("failed to parse pop value: %w", err)
	}

	*retTs = tmpTs
	*retValue = tmpValue

	return remaining, nil
}

func parsePush(data Bytes, retTs *time.Time, retCnt *int32) (remaining Bytes, err error) {
	if len(data) < TsLen+4 {
		return nil, utils.Errorf("invalid push data length: %d, expected at least %d", len(data), TsLen+4)
	}

	var tmpTs time.Time
	var tmpCnt int32

	tmpTs, remaining, err = parseTs(data)
	if err != nil {
		return nil, utils.Errorf("failed to parse push timestamp: %w", err)
	}
	tmpCnt, remaining, err = parseCnt(remaining)
	if err != nil {
		return nil, utils.Errorf("failed to parse push count: %w", err)
	}

	*retTs = tmpTs
	*retCnt = tmpCnt
	return remaining, nil
}

func parseTrim(data Bytes) (*LTrimHint, error) {
	if len(data) < TsLen+4 {
		return nil, utils.Errorf("invalid trim data length: %d, expected at least %d", len(data), TsLen+4)
	}
	ts, remaining, err := parseTs(data)
	if err != nil {
		return nil, utils.Errorf("failed to parse trim timestamp: %w", err)
	}

	cnt, remaining, err := parseCnt(remaining)
	if err != nil {
		return nil, utils.Errorf("failed to parse trim count: %w", err)
	}

	direction := 1 // default to left trim
	if cnt < 0 {
		direction = -1
	}

	hint := &LTrimHint{
		Ts:        ts,
		Direction: direction,
	}

	cnt = max(cnt, -cnt) // cnt = abs(cnt)
	for i := int32(0); i < cnt; i++ {
		var value Bytes
		remaining, err = parseBytes(remaining, &value)
		if err != nil {
			return nil, utils.Errorf("failed to parse trim value %d: %w", i, err)
		}
		hint.RemovedValues = append(hint.RemovedValues, value)
	}
	return hint, nil
}

func parseLrem(data Bytes) (*LRemHint, error) {
	if len(data) < TsLen+4 {
		return nil, utils.Errorf("invalid lrem data length: %d, expected at least %d", len(data), TsLen+4)
	}

	ts, remaining, err := parseTs(data)
	if err != nil {
		return nil, utils.Errorf("failed to parse lrem timestamp: %w", err)
	}

	cnt, remaining, err := parseCnt(remaining)
	if err != nil {
		return nil, utils.Errorf("failed to parse lrem count: %w", err)
	}

	hint := &LRemHint{
		Ts:           ts,
		Cnt:          cnt,
		RemovedValue: Bytes{},
	}

	if remaining, err = parseBytes(remaining, &hint.RemovedValue); err != nil {
		return nil, utils.Errorf("failed to parse lrem value: %w", err)
	}

	if len(remaining) != 0 {
		return nil, utils.Errorf("unexpected remaining data: %v", remaining)
	}

	return hint, nil
}
