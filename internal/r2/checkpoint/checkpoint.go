package checkpoint

import (
	"C"
	"fmt"
	"runtime"

	"git.xiaojukeji.com/foundation/fusion/gorocksdb"
)
import (
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/mergeoperator"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type CheckPoint struct {
	db       *gorocksdb.DB
	cfs      map[string]*gorocksdb.ColumnFamilyHandle
	dbOption *gorocksdb.Options
}

type ManagedIter struct {
	*gorocksdb.Iterator
	readOption *gorocksdb.ReadOptions
	prefix     []byte
	suffix     []byte
}

func (mi *ManagedIter) Close() {
	if mi.Iterator == nil {
		return
	}

	mi.Iterator.Close()
	mi.readOption.Destroy()
	mi.Iterator = nil
	mi.readOption = nil
	mi.suffix = []byte("<closed>")
}

func OpenCheckpointDB(dbPath string) (*CheckPoint, error) {
	cf_names := []string{"default"}
	cf_names = append(cf_names, r2.R2CFNames()...)

	opt := gorocksdb.NewDefaultOptions()
	opt.SetMergeOperator(&mergeoperator.MergeOperator{})
	runtime.SetFinalizer(opt, func(o *gorocksdb.Options) {
		if o == nil {
			return
		}
		o.Destroy()
	})

	opts := make([]*gorocksdb.Options, len(cf_names))
	for i := range opts {
		opts[i] = opt
	}

	db, cfHandles, err := gorocksdb.OpenDbForReadOnlyColumnFamilies(opt, dbPath, cf_names, opts, false)
	if err != nil {
		return nil, utils.Errorf("failed to open RocksDB at %s: %w", dbPath, err)
	}

	cfs := make(map[string]*gorocksdb.ColumnFamilyHandle, len(cfHandles))
	for i, name := range cf_names {
		cfs[name] = cfHandles[i]
	}

	runtime.SetFinalizer(db, func(db *gorocksdb.DB) {
		if db == nil {
			return
		}
		db.Close()
	})

	return &CheckPoint{
		db:  db,
		cfs: cfs,
	}, nil
}

func (cp *CheckPoint) String() string {
	result := fmt.Sprintf("Checkpoint{ Db: %s, Cfs: {", cp.db.Name())
	for name, cf := range cp.cfs {
		result += fmt.Sprintf("%s: %p, ", name, cf)
	}
	result += "}}"
	return result
}

func (cp *CheckPoint) getIter(r2cf r2.R2CF, slot int32) (*ManagedIter, error) {
	if slot < r2.MIN_R2_SLOT || slot > r2.MAX_R2_SLOT {
		return nil, fmt.Errorf("invalid slot: %d, must be in range [%d, %d]", slot, r2.MIN_R2_SLOT, r2.MAX_R2_SLOT)
	}

	readOption := gorocksdb.NewDefaultReadOptions()
	suffix := fmt.Appendf(nil, "%d%s\xff\xff\xff\xff\xff", slot, r2.SLOT_SPLITTER)
	readOption.SetIterateUpperBound(suffix)

	iter := cp.db.NewIteratorCF(readOption, cp.cfs[r2cf.String()])
	prefix := fmt.Appendf(nil, "%d%s", slot, r2.SLOT_SPLITTER)

	mIter := &ManagedIter{
		Iterator:   iter,
		readOption: readOption,
		prefix:     prefix,
		suffix:     suffix,
	}

	iter.Seek(mIter.prefix)
	return mIter, nil
}

func (cp *CheckPoint) GetStrIter(slot int32) (*ManagedIter, error) {
	return cp.getIter(r2.R2CF_String, slot)
}

func (cp *CheckPoint) GetZsetIter(slot int32) (*ManagedIter, error) {
	return cp.getIter(r2.R2CF_ZSet, slot)
}

func (cp *CheckPoint) GetHashSetIter(slot int32) (*ManagedIter, error) {
	return cp.getIter(r2.R2CF_HashSet, slot)
}

func (cp *CheckPoint) GetListIter(slot int32) (*ManagedIter, error) {
	return cp.getIter(r2.R2CF_List, slot)
}

func (cp *CheckPoint) GetSetIter(slot int32) (*ManagedIter, error) {
	return cp.getIter(r2.R2CF_Set, slot)
}

func (cp *CheckPoint) GetTTLIter(slot int32) (*ManagedIter, error) {
	return cp.getIter(r2.R2CF_TTL, slot)
}

func (cp *CheckPoint) Get(cf r2.R2CF, key []byte) (value *[]byte, err error) {
	if !r2.R2CFIsValid(cf) {
		return nil, utils.Errorf("invalid column family: %s", cf.String())
	}

	readOpt := gorocksdb.NewDefaultReadOptions()
	defer readOpt.Destroy()
	cfh := cp.cfs[cf.String()]

	res, err := cp.db.GetCF(readOpt, cfh, key)
	if err != nil {
		return nil, utils.Errorf("failed to get key %s from column family %s: %w", key, cf.String(), err)
	}
	defer res.Free()

	if !res.Exists() {
		return nil, nil
	}

	copied := utils.CopySlice(res.Data())
	return &copied, nil
}

func (cp *CheckPoint) NewIterator(cf r2.R2CF, upperBound []byte) (*ManagedIter, error) {
	if !r2.R2CFIsValid(cf) {
		return nil, utils.Errorf("invalid column family: %s", cf.String())
	}

	readOpt := gorocksdb.NewDefaultReadOptions()

	if upperBound != nil {
		readOpt.SetIterateUpperBound(upperBound)
	}

	cfh := cp.cfs[cf.String()]
	iter := cp.db.NewIteratorCF(readOpt, cfh)

	return &ManagedIter{
		Iterator:   iter,
		readOption: readOpt,
		suffix:     upperBound,
	}, nil
}

func (cp *CheckPoint) GetLatestSequenceNumber() uint64 {
	return cp.db.GetLatestSequenceNumber()
}
