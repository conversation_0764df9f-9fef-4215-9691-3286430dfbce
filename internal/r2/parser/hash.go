package parser

import (
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type HashItem struct {
	Slot  int32
	Key   []byte
	Field []byte
	Value BytesOpt
	Ts    time.Time
}

func parseHashValue(rawValue []byte) (value BytesOpt, ts time.Time, err error) {
	value, ts, _, err = ParseValue(rawValue)
	if err != nil {
		err = utils.Errorf("failed to parse hash value: %w", err)
	}
	return
}

func ParseHashKey(rawKey []byte) (slot int32, key []byte, field []byte, err error) {
	slot, key, remainingPtr, err := ParseKey(rawKey)
	if remainingPtr == nil {
		err = utils.Errorf("invalid hash key, no field found")
		return
	}

	field = *remainingPtr
	return
}

func ParseHashItem(rawKey, rawValue []byte) (*HashItem, error) {
	slot, key, field, err := ParseHashKey(rawKey)
	if err != nil {
		return nil, utils.Errorf("failed to parse hash key: %w", err)
	}

	value, ts, err := parseHashValue(rawValue)
	if err != nil {
		return nil, utils.Errorf("failed to parse hash value: %w", err)
	}

	return &HashItem{
		Slot:  slot,
		Key:   key,
		Field: field,
		Value: value,
		Ts:    ts,
	}, nil
}
