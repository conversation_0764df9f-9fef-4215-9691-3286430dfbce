package parser

import (
	"bytes"
	"strconv"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/samber/mo"
)

type BytesOpt = mo.Option[[]byte]

func SomeBytes[T []byte | string](b T) BytesOpt {
	return mo.Some([]byte(b))
}

func NoneBytes() BytesOpt {
	return mo.None[[]byte]()
}

func ParseSecTs(raw []byte) (time.Time, error) {
	if len(raw) != r2.SEC_TS_LEN {
		return time.Time{}, utils.Errorf("invalid sec timestamp length: %d, expected: %d", len(raw), r2.SEC_TS_LEN)
	}

	ts, err := strconv.ParseInt(string(raw), 10, 64)
	if err != nil {
		return time.Time{}, utils.Errorf("failed to parse sec timestamp: %w", err)
	}

	return time.Unix(ts, 0), nil
}

func ParseUsTs(raw []byte) (time.Time, error) {
	if len(raw) != r2.US_TS_LEN {
		return time.Time{}, utils.Errorf("invalid us timestamp length: %d, expected: %d", len(raw), r2.US_TS_LEN)
	}

	ts, err := strconv.ParseInt(string(raw), 10, 64)
	if err != nil {
		return time.Time{}, utils.Errorf("failed to parse us timestamp: %w", err)
	}

	return time.Unix(0, ts*int64(time.Microsecond)), nil
}

func minKeyLen() int {
	// Minimum key length is the length of the slot splitter + slot num and key
	return 1 + len(r2.SLOT_SPLITTER) + 1
}

// return (slot, key, remaining bytes)
func ParseKey(raw_key []byte) (int32, []byte, *[]byte, error) {
	MIN_KEY_LEN := minKeyLen()

	len_key := len(raw_key)
	if len_key < MIN_KEY_LEN {
		return 0, nil, nil, utils.Errorf("%w invalid key length: %d, expected at least %d", r2.ErrInvalidR2Key, len_key, MIN_KEY_LEN)
	}

	slot_splitter_pos := bytes.Index(raw_key, r2.SLOT_SPLITTER)
	if slot_splitter_pos < 0 {
		return 0, nil, nil, utils.Errorf("%w missing slot splitter in key: %s", r2.ErrInvalidR2Key, string(raw_key))
	}

	slot, err := strconv.ParseInt(string(raw_key[:slot_splitter_pos]), 10, 32)
	if err != nil {
		return 0, nil, nil, utils.Errorf("failed to parse slot from key: %s, error: %w", string(raw_key), err)
	}

	key_start := slot_splitter_pos + len(r2.SLOT_SPLITTER)
	key_part := raw_key[key_start:]

	key_splitter_pos := bytes.Index(key_part, []byte(r2.KEY_SPLITTER))
	var key []byte
	var remaining *[]byte

	if key_splitter_pos != -1 {
		key = key_part[:key_splitter_pos]
		remaining_start := key_splitter_pos + len(r2.KEY_SPLITTER)
		remaining_part := key_part[remaining_start:]
		remaining = &remaining_part
	} else {
		key = key_part
	}

	return int32(slot), key, remaining, nil
}

// return (value, timestamp, optional ttl, error)
func ParseValue(rawValue []byte) (value BytesOpt, ts time.Time, ttl mo.Option[time.Time], err error) {
	lenValue := len(rawValue)
	if lenValue == 0 {
		err = utils.Errorf("invalid value, length = 0")
		return
	}

	hasTTL := rawValue[lenValue-1] == r2.VALUE_TERMINATOR
	var valueEnd, tsStart, ttlStart int

	if hasTTL {
		minRequired := 1 + r2.SEC_TS_LEN + r2.TTL_LEN
		if lenValue < minRequired {
			err = utils.Errorf("invalid value length: %d, expected at least %d", lenValue, minRequired)
			return
		}
		valueEnd = lenValue - minRequired
		tsStart = valueEnd
		ttlStart = tsStart + r2.SEC_TS_LEN
	} else {
		if lenValue < r2.SEC_TS_LEN {
			err = utils.Errorf("invalid value length: %d, expected at least %d", lenValue, r2.SEC_TS_LEN)
			return
		}
		valueEnd = lenValue - r2.SEC_TS_LEN
		tsStart = valueEnd
	}

	if valueEnd != 0 {
		value = SomeBytes(rawValue[:valueEnd])
	} else {
		value = NoneBytes()
	}

	value = value.MapValue(func(v []byte) []byte {
		if bytes.Equal(v, r2.FUION_EMPTY) {
			return []byte{}
		}
		return v
	})

	tsPart := rawValue[tsStart : tsStart+r2.SEC_TS_LEN]
	if ts, err = ParseSecTs(tsPart); err != nil {
		err = utils.Errorf("failed to parse timestamp from raw value: %v", err)
		return
	}

	if hasTTL {
		ttlPart := rawValue[ttlStart : ttlStart+r2.TTL_LEN]
		var ttlTime time.Time
		if ttlTime, err = ParseUsTs(ttlPart); err != nil {
			err = utils.Errorf("failed to parse ttl from raw value: %v", err)
			return
		}
		ttl = mo.Some(ttlTime)
	}

	return
}
