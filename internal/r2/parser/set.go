package parser

import (
	"bytes"
	"encoding/hex"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type SetItem struct {
	Slot   int32
	Key    []byte
	Member []byte
	// Indicates if the member is deleted
	Deleted bool
	Ts      time.Time
}

var SET_VALUE_PREFIX = []byte("smember")

func parseSetValue(rawValue []byte) (isDeleted bool, ts time.Time, err error) {
	valOpt, ts, _, err := ParseValue(rawValue)
	if err != nil {
		return false, time.Time{}, utils.Errorf("failed to parse set value: %v", err)
	}

	val, ok := valOpt.Get()
	if !ok {
		return true, ts, nil // If value is empty, it indicates deletion
	}

	// todo: maybe we should check the valOpt == Some(SET_VALUE_PREFIX) to ensure it's a valid set value
	if !bytes.HasPrefix(val, SET_VALUE_PREFIX) {
		return false, ts, utils.Errorf("invalid set value prefix: %s, expected prefix: %s", val, SET_VALUE_PREFIX)
	}

	return false, ts, nil // If the value is valid and not empty, it indicates a normal set member
}

func ParseSetKey(rawKey []byte) (slot int32, key []byte, member []byte, err error) {
	slot, keyBytes, remainingPtr, err := ParseKey(rawKey)

	if remainingPtr == nil || len(*remainingPtr) == 0 {
		err = utils.Errorf("Invalid set key, no member found")
		return
	}

	remaining := *remainingPtr

	if !bytes.HasPrefix(remaining, []byte("\x00\x00")) {
		err = utils.Errorf("Invalid set key, expected member prefix, %s", hex.EncodeToString(remaining))
		return
	}

	remaining = remaining[2:] // Skip the prefix
	if bytes.Equal(remaining, r2.FUION_EMPTY) {
		member = []byte{}
	} else {
		member = remaining
	}

	key = keyBytes
	return
}

func ParseSetItem(rawKey, rawValue []byte) (*SetItem, error) {
	slot, key, member, err := ParseSetKey(rawKey)
	if err != nil {
		return nil, err
	}

	deleted, ts, err := parseSetValue(rawValue)
	if err != nil {
		return nil, err
	}

	return &SetItem{
		Slot:    slot,
		Key:     key,
		Member:  member,
		Deleted: deleted,
		Ts:      ts,
	}, nil
}
