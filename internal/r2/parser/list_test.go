package parser_test

import (
	"testing"
	"time"

	. "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseQueueMeta(t *testing.T) {
	rawKey := fromHex("31245e246c6b65793126266d657461")
	rawValue := fromHex("07000000fcffff7f02000080313734353732353539313137343537323535393138373730363024")

	expected := &QueueMeta{
		Slot:  1,
		Key:   []byte("lkey1"),
		Size:  7,
		Left:  2147483644,
		Right: 2147483650,
		Ts:    us2Ts(1745725591877060),
	}

	got, err := ParseQueueMeta(rawKey, rawValue)
	require.NoError(t, err)
	require.NotNil(t, got)

	assert.Equal(t, expected, got)
}

func TestParseQueueItem(t *testing.T) {
	type testCase struct {
		name     string
		rawKey   []byte
		rawValue []byte

		wantSlot  int32
		wantKey   []byte
		wantSeq   uint32
		wantValue BytesOpt
		wantTs    time.Time
	}

	cases := []testCase{
		{
			name:     "basic item 0",
			rawKey:   fromHex("31245e246c6b657931262600000080"),
			rawValue: fromHex("63313734353732353531303137343537323535313036303036393524"),

			wantSlot:  1,
			wantKey:   []byte("lkey1"),
			wantSeq:   0x80_00_00_00,
			wantValue: SomeBytes("c"),
			wantTs:    us2Ts(1745725510600695),
		},
		{
			name:     "basic item 1",
			rawKey:   fromHex("31245e246c6b657931262601000080"),
			rawValue: fromHex("63313734353732353531303137343537323535313036303036393524"),

			wantSlot:  1,
			wantKey:   []byte("lkey1"),
			wantSeq:   0x80_00_00_01,
			wantValue: SomeBytes("c"),
			wantTs:    us2Ts(1745725510600695),
		},
		{
			name:     "empty value",
			rawKey:   fromHex("31245e246c6b6579312626fcffff7f"),
			rawValue: fromHex("667573696f6e3a656d7074793a313734353732353539313137343537323535393138373730363024"),

			wantSlot:  1,
			wantKey:   []byte("lkey1"),
			wantSeq:   0x7f_ff_ff_fc,
			wantValue: SomeBytes(""),
			wantTs:    us2Ts(1745725591877060),
		},
		{
			name:     "basic item 2",
			rawKey:   fromHex("31245e246c6b6579312626ffffff7f"),
			rawValue: fromHex("61313734353732353530343137343537323535303431323339363924"),

			wantSlot:  1,
			wantKey:   []byte("lkey1"),
			wantSeq:   0x7f_ff_ff_ff,
			wantValue: SomeBytes("a"),
			wantTs:    us2Ts(1745725504123969),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseQueueItem(tc.rawKey, tc.rawValue)
			require.NoError(t, err)
			require.NotNil(t, got)

			assert.Equal(t, tc.wantSlot, got.Slot)
			assert.Equal(t, tc.wantKey, got.Key)
			assert.Equal(t, tc.wantSeq, got.Seq)
			assert.Equal(t, tc.wantValue, got.Value)
			assert.Equal(t, tc.wantTs, got.Ts)

		})
	}
}

func TestParseListMeta(t *testing.T) {
	rawKey := fromHex("32245e246c6b657932262643")
	rawValue := fromHex("3931373435373431383630")

	expected := &ListMeta{
		Slot: 2,
		Key:  []byte("lkey2"),
		Size: 9,
		Ts:   sec2Ts(1745741860),
	}

	got, err := ParseListMeta(rawKey, rawValue)
	require.NoError(t, err)
	require.NotNil(t, got)

	assert.Equal(t, expected.Slot, got.Slot)
	assert.Equal(t, expected.Key, got.Key)
	assert.Equal(t, expected.Size, got.Size)
	assert.Equal(t, expected.Ts, got.Ts)
}

func TestParseListItem(t *testing.T) {
	type testCase struct {
		name     string
		rawKey   []byte
		rawValue []byte

		wantSlot  int32
		wantKey   []byte
		wantSeq   []byte
		wantValue BytesOpt
		wantTs    time.Time
	}

	cases := []testCase{
		{
			name:     "basic item 0",
			rawKey:   fromHex("32245e246c6b6579322626444c30303632313031363531373231394444334646464646464630303030"),
			rawValue: fromHex("667573696f6e3a656d7074793a31373435373431383430"),

			wantSlot:  2,
			wantKey:   []byte("lkey2"),
			wantSeq:   []byte("DL00621016517219DD3FFFFFFF0000"),
			wantValue: SomeBytes(""),
			wantTs:    sec2Ts(1745741840),
		},
		{
			name:      "basic item 1",
			rawKey:    fromHex("32245e246c6b6579322626444c30303632313031363531373231394444334646464646464630303031"),
			rawValue:  fromHex("63636331373435373431383430"),
			wantSlot:  2,
			wantKey:   []byte("lkey2"),
			wantSeq:   []byte("DL00621016517219DD3FFFFFFF0001"),
			wantValue: SomeBytes("ccc"),
			wantTs:    sec2Ts(1745741840),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseListItem(tc.rawKey, tc.rawValue)
			require.NoError(t, err)
			require.NotNil(t, got)

			assert.Equal(t, tc.wantSlot, got.Slot)
			assert.Equal(t, tc.wantKey, got.Key)
			assert.Equal(t, tc.wantSeq, got.Seq)
			assert.Equal(t, tc.wantValue, got.Value)
			assert.Equal(t, tc.wantTs, got.Ts)

		})
	}
}
