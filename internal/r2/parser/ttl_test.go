package parser_test

import (
	"encoding/hex"
	"testing"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	. "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/stretchr/testify/require"
)

func TestParseTTL(t *testing.T) {
	rawKey, err := hex.DecodeString("32245e246c6b657932")
	require.NoError(t, err)
	rawValue, err := hex.DecodeString("353137343732373335323339373439353831373436373733353233")
	require.NoError(t, err)

	expected := &TTLItem{
		Slot:  2,
		Key:   []byte("lkey2"),
		Cf:    r2.R2CF_List,
		ExpAt: us2Ts(1747273523974958),
		Ts:    sec2Ts(1746773523),
	}
	got, err := ParseTTLItem(rawKey, rawValue)
	require.NoError(t, err)
	require.NotNil(t, got)

	require.Equal(t, expected.Slot, got.Slot)
	require.Equal(t, expected.Key, got.Key)
	require.Equal(t, expected.Cf, got.Cf)
	require.Equal(t, expected.ExpAt, got.ExpAt)
	require.Equal(t, expected.Ts, got.Ts)
}
