package parser

import (
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type TTLItem struct {
	Slot  int32
	Key   []byte
	Cf    r2.R2CF
	ExpAt time.Time
	Ts    time.Time
}

func ParseTTLItem(rawKey, rawValue []byte) (*TTLItem, error) {
	if len(rawValue) != r2.SEC_TS_LEN+r2.US_TS_LEN+1 {
		return nil, utils.Errorf("invalid TTL value length: %d, expected %d", len(rawValue), r2.SEC_TS_LEN+r2.US_TS_LEN+1)
	}

	slot, key, _, err := ParseKey(rawKey)
	if err != nil {
		return nil, utils.Errorf("failed to parse TTL key: %w", err)
	}

	cfStr := rawValue[0]
	cf := r2.R2CF(cfStr - '0')

	if !r2.R2CFIsValid(cf) {
		return nil, utils.Errorf("invalid R2CF value: %d", cf)
	}

	expAt, err := ParseUsTs(rawValue[1 : 1+r2.US_TS_LEN])
	if err != nil {
		return nil, utils.Errorf("failed to parse expiration timestamp: %w", err)
	}

	ts, err := ParseSecTs(rawValue[1+r2.US_TS_LEN:])
	if err != nil {
		return nil, utils.Errorf("failed to parse timestamp: %w", err)
	}

	return &TTLItem{
		Slot:  slot,
		Key:   key,
		Cf:    cf,
		ExpAt: expAt,
		Ts:    ts,
	}, nil

}
