package parser

import (
	"encoding/binary"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/samber/mo"
	"github.com/shopspring/decimal"
)

// type DecimalOpt = mo.Option[decimal.Decimal]
type DecimalOpt struct {
	mo.Option[decimal.Decimal]
}

func (do DecimalOpt) Equal(other DecimalOpt) bool {
	d1, ok1 := do.Get()
	d2, ok2 := other.Get()

	if ok1 != ok2 {
		return false
	}

	return d1.Equal(d2)
}

func (do DecimalOpt) String() string {
	if d, ok := do.Get(); ok {
		return d.String()
	}
	return "None"
}

type ZSetItem struct {
	Slot   int32
	Key    []byte
	Member []byte
	Score  DecimalOpt
	Ts     time.Time
}

const gPRECISION = 18
const SCORE_LEN = 17

func invertBytes(b []byte) []byte {
	inverted := make([]byte, len(b))
	for i := range b {
		inverted[i] = ^b[i]
	}
	return inverted
}

func parseScore(rawValue []byte) (score decimal.Decimal, err error) {
	if len(rawValue) != SCORE_LEN {
		err = utils.Errorf("invalid zset score length: %d, expected %d", len(rawValue), SCORE_LEN)
		return
	}

	positive := true

	switch rawValue[0] {
	case '=':
		positive = true
	case '-':
		positive = false
	default:
		err = utils.Errorf("invalid zset score prefix: %c, expected '=' or '-'", rawValue[0])
		return
	}

	integerPart := rawValue[1:9]
	fractionPart := rawValue[9:17]

	if !positive {
		integerPart = invertBytes(integerPart)
		fractionPart = invertBytes(fractionPart)
	}

	integer := binary.BigEndian.Uint64(integerPart)
	fraction := binary.BigEndian.Uint64(fractionPart)

	decimalFraction := decimal.NewFromUint64(fraction).Div(decimal.NewFromInt(1).Shift(int32(gPRECISION)))
	decimalIngeter := decimal.NewFromUint64(integer)

	score = decimalIngeter.Add(decimalFraction)
	if !positive {
		score = score.Neg()
	}

	return
}

func parseZsetValue(rawValue []byte) (scoreOpt mo.Option[decimal.Decimal], ts time.Time, err error) {
	valOpt, ts, _, err := ParseValue(rawValue)
	if err != nil {
		err = utils.Errorf("failed to parse zset value: %w", err)
		return
	}

	rawValue, ok := valOpt.Get()
	if !ok {
		return
	}

	score, err := parseScore(rawValue)
	if err != nil {
		err = utils.Errorf("failed to parse zset score: %w", err)
		return
	}

	scoreOpt = mo.Some(score)
	return
}

func ParseZsetKey(rawKey []byte) (slot int32, key []byte, member []byte, err error) {
	slot, key, remainingPtr, err := ParseKey(rawKey)
	if err != nil {
		err = utils.Errorf("failed to parse zset key: %w", err)
		return
	}

	if remainingPtr == nil {
		err = utils.Errorf("invalid zset key, no member found")
		return
	}

	remaining := *remainingPtr
	if remaining[len(remaining)-1] != 's' {
		err = utils.Errorf("invalid zset key, expected 's' at the end, got '%c'", remaining[len(remaining)-1])
		return
	}

	member = remaining[:len(remaining)-3]
	return
}

func ParseZsetItem(rawKey, rawValue []byte) (*ZSetItem, error) {
	slot, key, member, err := ParseZsetKey(rawKey)
	if err != nil {
		return nil, utils.Errorf("failed to parse zset key: %w", err)
	}

	score, ts, err := parseZsetValue(rawValue)
	if err != nil {
		return nil, utils.Errorf("failed to parse zset value: %w", err)
	}

	return &ZSetItem{
		Slot:   slot,
		Key:    key,
		Member: member,
		Score:  DecimalOpt{score},
		Ts:     ts,
	}, nil
}
