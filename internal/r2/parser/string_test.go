package parser_test

import (
	"encoding/hex"
	"testing"
	"time"

	. "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/samber/mo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func sec2Ts(sec int64) time.Time {
	return time.Unix(sec, 0)
}

func us2Ts(us int64) time.Time {
	return time.Unix(0, us*int64(time.Microsecond))
}

func fromHex(s string) []byte {
	b, err := hex.DecodeString(s)
	if err != nil {
		panic(err)
	}
	return b
}

func TestParseString(t *testing.T) {
	type testCase struct {
		name     string
		rawKey   []byte
		rawValue []byte

		wantSlot  int32
		wantKey   []byte
		wantValue BytesOpt
		wantTs    time.Time
		wantTtl   mo.Option[time.Time]
	}

	cases := []testCase{
		{
			name:      "basic without TTL",
			rawKey:    []byte("1$^$key1"),
			rawValue:  []byte("b1744873303"),
			wantSlot:  1,
			wantKey:   []byte("key1"),
			wantValue: SomeBytes([]byte("b")),
			wantTs:    sec2Ts(1744873303),
			wantTtl:   mo.None[time.Time](),
		},
		{
			name:      "with TTL",
			rawKey:    []byte("2$^$key2"),
			rawValue:  []byte("ccc17448825241744883635364542$"),
			wantSlot:  2,
			wantKey:   []byte("key2"),
			wantValue: SomeBytes([]byte("ccc")),
			wantTs:    sec2Ts(1744882524),
			wantTtl:   mo.Some(us2Ts(1744883635364542)),
		},
		{
			name:      "empty value with TTL",
			rawKey:    []byte("3$^$key3"),
			rawValue:  []byte("fusion:empty:17449578381744958949627715$"),
			wantSlot:  3,
			wantKey:   []byte("key3"),
			wantValue: SomeBytes([]byte{}),
			wantTs:    sec2Ts(1744957838),
			wantTtl:   mo.Some(us2Ts(1744958949627715)),
		},
		{
			name:      "none Value",
			rawKey:    []byte("4$^$key4"),
			rawValue:  []byte("17449578381744958949627715$"),
			wantSlot:  4,
			wantKey:   []byte("key4"),
			wantValue: NoneBytes(),
			wantTs:    sec2Ts(1744957838),
			wantTtl:   mo.Some(us2Ts(1744958949627715)),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseStringItem(tc.rawKey, tc.rawValue)
			require.NoError(t, err)
			require.NotNil(t, got)

			assert.Equal(t, tc.wantSlot, got.Slot)
			assert.Equal(t, tc.wantKey, got.Key)
			assert.Equal(t, tc.wantValue, got.Value)
			assert.Equal(t, tc.wantTs, got.Ts)
			assert.Equal(t, tc.wantTtl, got.Ttl)

		})
	}
}
