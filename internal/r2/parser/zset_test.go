package parser_test

import (
	"testing"
	"time"

	. "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/samber/mo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"
)

func str2decimalOpt(s string) DecimalOpt {
	d, err := decimal.NewFromString(s)
	if err != nil {
		panic(err)
	}
	return DecimalOpt{mo.Some(d)}
}

func TestParseZset(t *testing.T) {
	type testCase struct {
		name     string
		rawKey   []byte
		rawValue []byte

		wantSlot   int32
		wantKey    []byte
		wantMember []byte
		wantScore  DecimalOpt
		wantTs     time.Time
	}

	cases := []testCase{
		{
			name:     "zset item with positive score",
			rawKey:   fromHex("31245e2472756e6f6f626b657926267265646973000573"),
			rawValue: fromHex("3d0000000000000001000000000000000031373435323333363132"),

			wantSlot:   1,
			wantKey:    []byte("runoobkey"),
			wantMember: []byte("redis"),
			wantScore:  str2decimalOpt("1"),
			wantTs:     sec2Ts(1745233612),
		},
		{
			name:     "zset item with negative score",
			rawKey:   fromHex("31245e2472756e6f6f626b65792626616161000373"),
			rawValue: fromHex("2dfffffffffffffffdffffffffffffffff31373435323334383534"),

			wantSlot:   1,
			wantKey:    []byte("runoobkey"),
			wantMember: []byte("aaa"),
			wantScore:  str2decimalOpt("-2"),
			wantTs:     sec2Ts(1745234854),
		},
		{
			name:     "zset with fractional score",
			rawKey:   fromHex("31245e2472756e6f6f626b65792626626262000373"),
			rawValue: fromHex("3d00000000000000010429d069189e000031373435343737313832"),

			wantSlot:   1,
			wantKey:    []byte("runoobkey"),
			wantMember: []byte("bbb"),
			wantScore:  str2decimalOpt("1.3"),
			wantTs:     sec2Ts(1745477182),
		},
		{
			name:     "zset with positive and fractional score",
			rawKey:   fromHex("31245e2472756e6f6f626b65792626636363000373"),
			rawValue: fromHex("2dfffffffffffffffefa72ea1e89d7ffff31373435343737313838"),

			wantSlot:   1,
			wantKey:    []byte("runoobkey"),
			wantMember: []byte("ccc"),
			wantScore:  str2decimalOpt("-1.4"),
			wantTs:     sec2Ts(1745477188),
		},
		{
			name:     "zset basic",
			rawKey:   fromHex("31245e2472756e6f6f626b657926266d6f6e676f000573"),
			rawValue: fromHex("3d0000000000000002000000000000000031373435323333363230"),

			wantSlot:   1,
			wantKey:    []byte("runoobkey"),
			wantMember: []byte("mongo"),
			wantScore:  str2decimalOpt("2"),
			wantTs:     sec2Ts(1745233620),
		},
		{
			name:     "zset with empty member",
			rawKey:   fromHex("31245e2472756e6f6f626b65792626000073"),
			rawValue: fromHex("3d0000000000000003000000000000000031373435323333363237"),

			wantSlot:   1,
			wantKey:    []byte("runoobkey"),
			wantMember: []byte{},
			wantScore:  str2decimalOpt("3"),
			wantTs:     sec2Ts(1745233627),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseZsetItem(tc.rawKey, tc.rawValue)
			require.NoError(t, err)
			require.NotNil(t, got)

			require.Equal(t, tc.wantSlot, got.Slot)
			require.Equal(t, tc.wantKey, got.Key)
			require.Equal(t, tc.wantMember, got.Member)

			require.True(t, tc.wantScore.Equal(got.Score), "expected score %s, got %s", tc.wantScore.String(), got.Score.String())
			require.Equal(t, tc.wantTs, got.Ts)

		})
	}
}
