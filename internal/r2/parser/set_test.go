package parser_test

import (
	"testing"
	"time"

	. "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseSet(t *testing.T) {
	type testCase struct {
		name     string
		rawKey   []byte
		rawValue []byte

		wantSlot    int32
		wantKey     []byte
		wantDeleted bool
		wantMember  []byte
		wantTs      time.Time
	}

	cases := []testCase{
		{
			name:     "basic set item",
			rawKey:   []byte("1$^$set_key1&&\x00\x00aaa"),
			rawValue: []byte("smember1744957295"),

			wantSlot:    1,
			wantKey:     []byte("set_key1"),
			wantMember:  []byte("aaa"),
			wantDeleted: false,
			wantTs:      sec2Ts(1744957295),
		},
		{
			name:     "set item with empty member",
			rawKey:   []byte("1$^$set_key1&&\x00\x00fusion:empty:"),
			rawValue: []byte("smember1744957295"),

			wantSlot:    1,
			wantKey:     []byte("set_key1"),
			wantMember:  []byte{},
			wantDeleted: false,
			wantTs:      sec2Ts(1744957295),
		},
		{
			name:     "set item with deleted member",
			rawKey:   []byte("1$^$set_key1&&\x00\x00fusion:empty:"),
			rawValue: []byte("1744957295"),

			wantSlot:    1,
			wantKey:     []byte("set_key1"),
			wantMember:  []byte{},
			wantDeleted: true,
			wantTs:      sec2Ts(1744957295),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseSetItem(tc.rawKey, tc.rawValue)
			require.NoError(t, err)
			require.NotNil(t, got)

			assert.Equal(t, tc.wantSlot, got.Slot)
			assert.Equal(t, tc.wantKey, got.Key)
			assert.Equal(t, tc.wantMember, got.Member)
			assert.Equal(t, tc.wantTs, got.Ts)
		})
	}
}
