package parser

import (
	"bytes"
	"encoding/binary"
	"strconv"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type QueueMeta struct {
	Slot  int32
	Key   []byte
	Size  uint32
	Left  uint32
	Right uint32
	Ts    time.Time
}

var (
	QUEUE_META_SUFFIX = []byte("meta")
	LIST_META_SUFFIX  = []byte("C")
)

const (
	// value format: f'{size}{left}{right}{ts(sec)}{ts(us)}$'
	//
	// the size, left, right, ts are all u32, little endian
	QUEUE_META_VALUE_LEN = 4 * 3

	QUEUE_ITEM_VALUE_MIN_LEN = r2.SEC_TS_LEN + r2.US_TS_LEN + 1
)

type ListVal interface {
	GetTs() time.Time
	GetVal() BytesOpt
	GetSeq() []byte
	GetParseFunc() ListItemParseFunc
}

type ListItemParseFunc func(rawKey, rawValue []byte) (ListVal, error)

func IsQueueMetaKey(rawKey []byte) bool {
	return bytes.HasSuffix(rawKey, QUEUE_META_SUFFIX)
}

func IsListMetaKey(rawKey []byte) bool {
	return bytes.HasSuffix(rawKey, LIST_META_SUFFIX)
}

func parseQueueMetaValue(value []byte) (size uint32, left uint32, right uint32, err error) {
	if len(value) != QUEUE_META_VALUE_LEN {
		err = utils.Errorf("invalid queue meta value length: %d, expected %d", len(value), QUEUE_META_VALUE_LEN)
		return
	}

	size = binary.LittleEndian.Uint32(value[:4])
	left = binary.LittleEndian.Uint32(value[4:8])
	right = binary.LittleEndian.Uint32(value[8:])
	return
}

func ParseQueueMeta(rawKey, rawValue []byte) (*QueueMeta, error) {
	if !IsQueueMetaKey(rawKey) {
		return nil, utils.Errorf("invalid queue meta key: %s", rawKey)
	}

	slot, key, _, err := ParseKey(rawKey)
	if err != nil {
		return nil, utils.Errorf("failed to parse queue meta key: %w", err)
	}

	valOpt, ts, usTs, err := ParseValue(rawValue)
	if err != nil {
		return nil, utils.Errorf("failed to parse queue meta value: %w", err)
	}

	ts = usTs.OrElse(ts)

	meta := &QueueMeta{
		Slot: slot,
		Key:  key,
		Ts:   ts,
	}

	if val, ok := valOpt.Get(); ok {
		size, left, right, err := parseQueueMetaValue(val)
		if err != nil {
			return nil, err
		}
		meta.Size = size
		meta.Left = left
		meta.Right = right
	}
	return meta, nil
}

type QueueItem struct {
	Slot  int32
	Key   []byte
	Seq   uint32
	Value BytesOpt
	Ts    time.Time
}

// value format: f'{val}{ts(sec)}{ts(us)}$'
func parseQueueItemValue(rawValue []byte) (value BytesOpt, ts time.Time, err error) {
	value, ts, usTs, err := ParseValue(rawValue)
	if err != nil {
		err = utils.Errorf("failed to parse queue item value: %w", err)
	}
	ts = usTs.OrElse(ts)
	return
}

func ParseQueueItem(rawKey, rawValue []byte) (*QueueItem, error) {
	slot, key, rawSeqPtr, err := ParseKey(rawKey)
	if err != nil {
		return nil, utils.Errorf("failed to parse queue item key: %w", err)
	}

	if rawSeqPtr == nil {
		return nil, utils.Errorf("invalid queue item key: %s, missing sequence number", rawKey)
	}

	rawSeq := *rawSeqPtr
	if len(rawSeq) != 4 {
		return nil, utils.Errorf("invalid queue item key sequence number length: %d, expected 4", len(rawSeq))
	}

	seq := binary.LittleEndian.Uint32(rawSeq)

	value, ts, err := parseQueueItemValue(rawValue)
	if err != nil {
		return nil, err
	}

	return &QueueItem{
		Slot:  slot,
		Key:   key,
		Seq:   seq,
		Value: value,
		Ts:    ts,
	}, nil
}

type ListMeta struct {
	Slot int32
	Key  []byte
	Size int64
	Ts   time.Time
}

func ParseListMeta(rawKey, rawValue []byte) (*ListMeta, error) {
	slot, key, remainingPtr, err := ParseKey(rawKey)

	if err != nil {
		return nil, utils.Errorf("failed to parse list meta key: %w", err)
	}

	if remainingPtr == nil {
		return nil, utils.Errorf("invalid list meta key: %s, missing suffix", rawKey)
	}

	remaining := *remainingPtr
	if !bytes.Equal(remaining, LIST_META_SUFFIX) {
		return nil, utils.Errorf("invalid list meta key suffix: %s, expected 'C'", remaining)
	}

	valOpt, ts, _, err := ParseValue(rawValue)
	if err != nil {
		return nil, utils.Errorf("failed to parse list meta value: %w", err)
	}

	size := int64(0)
	if val, ok := valOpt.Get(); ok {
		size, err = strconv.ParseInt(string(val), 10, 64)
	}

	if err != nil {
		return nil, utils.Errorf("failed to parse size from list meta value: %w", err)
	}

	return &ListMeta{
		Slot: slot,
		Key:  key,
		Size: size,
		Ts:   ts,
	}, nil
}

type ListItem struct {
	Slot  int32
	Key   []byte
	Seq   []byte
	Value BytesOpt
	Ts    time.Time
}

func parseListItemValue(rawValue []byte) (valOpt BytesOpt, ts time.Time, err error) {
	return parseQueueItemValue(rawValue)
}

func ParseListItem(rawKey, rawValue []byte) (*ListItem, error) {
	slot, key, remaining, err := ParseKey(rawKey)
	if err != nil {
		return nil, utils.Errorf("failed to parse list item key: %w", err)
	}

	if remaining == nil || (*remaining)[0] != 'D' {
		return nil, utils.Errorf("invalid list item key: %s, missing 'D' prefix in sequence", rawKey)
	}

	seq := *remaining
	value, ts, err := parseListItemValue(rawValue)

	if err != nil {
		return nil, err
	}

	return &ListItem{
		Slot:  slot,
		Key:   key,
		Seq:   seq,
		Value: value,
		Ts:    ts,
	}, nil
}

// impl ListVal for ListItem
func (li *ListItem) GetTs() time.Time {
	return li.Ts
}
func (li *ListItem) GetVal() BytesOpt {
	return li.Value
}
func (li *ListItem) GetSeq() []byte {
	return li.Seq
}
func (*ListItem) GetParseFunc() ListItemParseFunc {
	return func(rawKey, rawValue []byte) (ListVal, error) {
		return ParseListItem(rawKey, rawValue)
	}
}

// impl ListVal for QueueItem
func (li *QueueItem) GetTs() time.Time {
	return li.Ts
}
func (li *QueueItem) GetVal() BytesOpt {
	return li.Value
}
func (li *QueueItem) GetSeq() []byte {
	seq := make([]byte, 4)
	binary.LittleEndian.PutUint32(seq, li.Seq)
	return seq
}
func (*QueueItem) GetParseFunc() ListItemParseFunc {
	return func(rawKey, rawValue []byte) (ListVal, error) {
		return ParseQueueItem(rawKey, rawValue)
	}
}
