package parser_test

import (
	"testing"
	"time"

	. "git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseHash(t *testing.T) {
	type testCase struct {
		name      string
		rawKey    []byte
		rawValue  []byte
		wantSlot  int32
		wantKey   []byte
		wantField []byte
		wantValue BytesOpt
		wantTs    time.Time
	}
	cases := []testCase{
		{
			name:     "hash item with empty field",
			rawKey:   []byte("1$^$hkey_1&&"),
			rawValue: []byte("value21744963073"),

			wantSlot:  1,
			wantKey:   []byte("hkey_1"),
			wantField: []byte{},
			wantValue: SomeBytes("value2"),
			wantTs:    sec2Ts(1744963073),
		},
		{
			name:     "hash item",
			rawKey:   []byte("1$^$hkey_1&&field1"),
			rawValue: []byte("value11744963059"),

			wantSlot:  1,
			wantKey:   []byte("hkey_1"),
			wantField: []byte("field1"),
			wantValue: SomeBytes("value1"),
			wantTs:    sec2Ts(1744963059),
		},
		{
			name:     "hash item with empty value",
			rawKey:   []byte("1$^$hkey_1&&field2"),
			rawValue: []byte("fusion:empty:1744963066"),

			wantSlot:  1,
			wantKey:   []byte("hkey_1"),
			wantField: []byte("field2"),
			wantValue: SomeBytes(""),
			wantTs:    sec2Ts(1744963066),
		},
		{
			name:     "hash item with deleted value",
			rawKey:   []byte("1$^$hkey_1&&field2"),
			rawValue: []byte("1744963066"),

			wantSlot:  1,
			wantKey:   []byte("hkey_1"),
			wantField: []byte("field2"),
			wantValue: NoneBytes(),
			wantTs:    sec2Ts(1744963066),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseHashItem(tc.rawKey, tc.rawValue)
			require.NoError(t, err)
			require.NotNil(t, got)

			assert.Equal(t, tc.wantSlot, got.Slot)
			assert.Equal(t, tc.wantKey, got.Key)
			assert.Equal(t, tc.wantField, got.Field)
			assert.Equal(t, tc.wantValue, got.Value)
			assert.Equal(t, tc.wantTs, got.Ts)
		})
	}
}
