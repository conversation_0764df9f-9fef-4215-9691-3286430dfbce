package parser

import (
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/samber/mo"
)

type StringItem struct {
	Slot  int32
	Key   []byte
	Value BytesOpt
	Ts    time.Time
	Ttl   mo.Option[time.Time]
}

func ParseStringItem(rawKey, rawValue []byte) (*StringItem, error) {
	slot, key, remainingPtr, err := ParseKey(rawKey)
	if err != nil {
		return nil, err
	}

	value, ts, ttl, err := ParseValue(rawValue)
	if err != nil {
		return nil, err
	}

	if remainingPtr != nil {
		return nil, utils.Errorf("unexpected remaining bytes after parsing key: %v", remainingPtr)
	}

	return &StringItem{
		Slot:  slot,
		Key:   key,
		Value: value,
		Ts:    ts,
		Ttl:   ttl,
	}, nil
}
