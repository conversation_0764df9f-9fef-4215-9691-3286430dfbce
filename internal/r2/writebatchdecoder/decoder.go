package writebatchdecoder

import (
	"bytes"
	"errors"
	"math"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/consts"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	pseudohlc "git.xiaojukeji.com/fusion/fusion-syncer/internal/pseudo_hlc"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/cmdbuilder"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/writebatch"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/samber/mo"
)

type CCB = cmdbuilder.CmdBuilderBuilder
type CB = cmdbuilder.CmdBuilder
type WBE = writebatch.WriteBatchEntry
type TsOpt = writebatch.TsOpt
type EntryOpt = mo.Option[*entry.Entry]

func SomeEntry(e *entry.Entry) EntryOpt {
	return mo.Some(e)
}

func NoneEntry() EntryOpt {
	return mo.None[*entry.Entry]()
}

const MAX_BACKWARD_SEC = 60 * 60 * 24 * 3 // 3 day

type WbConverter struct {
	tableName    string
	serverID     string
	hlcConverter pseudohlc.HlcConverter
	useQueue     bool
}

const KVROCKS_LIST_ORIGIN uint64 = math.MaxUint64 >> 1

type listType string

const (
	listTypeList  listType = "lt_list"
	listTypeQueue listType = "lt_queue"
)

func NewWbConverter(serverID string, tableName string, useQueue bool) *WbConverter {
	converter := pseudohlc.GetHlcConverter(tableName, MAX_BACKWARD_SEC)

	return &WbConverter{
		serverID:     serverID,
		tableName:    tableName,
		hlcConverter: converter,
		useQueue:     useQueue,
	}
}

func (wbc *WbConverter) ConvertWb2Entry(seq uint64, logNum int64, wb *writebatch.WriteBatch) ([]*entry.Entry, error) {
	if len(wb.Entries) == 0 {
		return nil, nil
	}

	logger := logger.Logger().
		With("tableName", wbc.tableName).
		With("serverID", wbc.serverID).
		With("wbSeq", seq).
		With("logNum", logNum)

	if seq != wb.Seq {
		err := utils.Errorf("seq mismatch: expected %d, got %d", seq, wb.Seq)
		logger.Error(err)
		return nil, err
	}

	skip := wb.ReplFlag || wb.SyncFlag
	keys := []string{}

	// key -> entry
	entries := map[string]*entry.Entry{}

	for i := range wb.Entries {
		wbEntry := &wb.Entries[i]
		cf, err := r2.R2CFFromPrimitive(wbEntry.CfID)
		if err != nil {
			logger.Error(err)
			return nil, err
		}

		if cf == r2.R2CF_ZSet && bytes.HasSuffix(wbEntry.Key, []byte("z")) {
			logger.Debugf("skipping zset item, key: %s, which is a score key", utils.EscapeBytes(wbEntry.Key))
			continue
		}

		_, keyBytes, _, err := parser.ParseKey(wbEntry.Key)
		if err != nil {
			return nil, utils.Errorf("failed to parse key: %w", err)
		}

		key := string(keyBytes)
		if _, exist := entries[key]; !exist {
			entries[key] = entry.NewEntryWithSeq(key, seq, uint64(logNum))
		}
		keys = append(keys, key)

		if skip {
			// If the write batch is a replication or sync batch, we skip the entry
			// and do not convert it to an entry.Command, just convert it to an empty
			// command but still keep the key in the entry.

			entries[key].LastSeq = max(entries[key].LastSeq, wbEntry.Seq)
			continue
		}

		cbb := cmdbuilder.NewCmdBuilderBuilder(wbc.serverID, wbEntry.Seq, wbc.hlcConverter)
		var cmd entry.Command

		switch cf {
		case r2.R2CF_String:
			cmd, err = wbc.convertString(cbb, wb.Ts, wbEntry, key)
		case r2.R2CF_HashSet:
			cmd, err = wbc.convertHashSet(cbb, wb.Ts, wbEntry, key)
		case r2.R2CF_ZSet:
			cmd, err = wbc.convertZSet(cbb, wb.Ts, wbEntry, key)
		case r2.R2CF_Set:
			cmd, err = wbc.convertSet(cbb, wb.Ts, wbEntry, key)
		case r2.R2CF_TTL:
			cmd, err = wbc.convertTtl(cbb, key, wb.Ts, wbEntry, wb.PersistFlag)
		case r2.R2CF_List:
			continue
		default:
			logger.Errorf("unsupported cf: %s", cf)
			continue
		}

		if errors.Is(err, consts.ErrIgnoredWriteBatch) {
			logger.Debug(err)
			continue
		}

		if err != nil {
			return nil, err
		}

		entries[key].AddCmd(cmd)
	}
	if !skip {
		listEntries := utils.Filter(wb.Entries, func(e writebatch.WriteBatchEntry) bool {
			return e.CfID == uint32(r2.R2CF_List)
		})

		if len(listEntries) > 0 {
			entryOpt, err := wbc.convertList(logNum, wb.Ts, wb.Seq, wb.ListHints, listEntries)
			if err != nil {
				logger.Error(err)
				return nil, err
			}

			if entry, ok := entryOpt.Get(); ok {
				if existedEntry, ok := entries[entry.Key]; ok {
					existedEntry.Merge(entry)
				} else {
					entries[entry.Key] = entry
				}
			}
		}
	} else {
		logger.Debugf("skipping write batch conversion, write batch is a replication or sync batch, seq: %d, associated keys: %v, repl flag: %v, sync flag: %v", seq, keys, wb.ReplFlag, wb.SyncFlag)
	}

	res := []*entry.Entry{}
	for _, e := range entries {
		res = append(res, e)
	}

	return res, nil
}

func (wbc *WbConverter) convertString(cbb CCB, wbTsOpt TsOpt, wbEntry *WBE, key string) (entry.Command, error) {
	zero := entry.Command{}
	cmd := zero

	switch wbEntry.OpType {
	case writebatch.OpTypePut:
		{
			strItem, err := parser.ParseStringItem(wbEntry.Key, wbEntry.Value)
			if err != nil {
				return zero, err
			}

			strValue := strItem.Value.OrEmpty()
			strTs := strItem.Ts

			if ttl, ok := strItem.Ttl.Get(); ok {
				expireOpTs := wbTsOpt.OrElse(strTs)
				// If TTL is set, use PSETEXPIREAT_CMD to set the value
				// __psetexat key expireMs  value  modify_hlc modify_expire_hlc serverID seq
				cmd = cbb(r2.PSETEXPIREAT_CMD, key).
					AddSeg(strTs, ttl.UnixMilli(), strValue).
					AddSeg(expireOpTs).
					Build()
			} else {
				// If no TTL is set, use SET_CMD to set the value
				cmd = cbb(r2.SET_CMD, key).
					AddSegs(cmdbuilder.NewCmdSeg(strTs, strValue)).
					Build()
			}
		}
	case writebatch.OpTypeDel:
		{
			ts, ok := wbTsOpt.Get()
			if !ok {
				return zero, utils.Errorf("missing timestamp for delete operation:%w", consts.ErrMissingTimestamp)
			}
			cmd = cbb(r2.DEL_CMD, key).
				AddSegs(cmdbuilder.NewCmdSeg(ts)).
				Build()
		}
	case writebatch.OpTypeMerge:
		{
			ts, err := parser.ParseSecTs(wbEntry.Value)
			if err != nil {
				return zero, utils.Errorf("failed to parse timestamp from value: %w", err)
			}
			cmd = cbb(r2.DEL_CMD, key).
				AddSegs(cmdbuilder.NewCmdSeg(ts)).
				Build()
		}
	}
	return cmd, nil
}

func (wbc *WbConverter) convertTtl(cbb CCB, key string, wbTsOpt TsOpt, wbEntry *WBE, persistHint bool) (cmd entry.Command, err error) {
	zero := entry.Command{}
	if !persistHint {
		if wbEntry.OpType != writebatch.OpTypePut {
			err = utils.Errorf("but without persist flag, skip conversion: %w", consts.ErrIgnoredWriteBatch)
			return
		}
		// now, it must be a put
		ttlItem, err := parser.ParseTTLItem(wbEntry.Key, wbEntry.Value)
		if err != nil {
			return zero, err
		}
		cmd = cbb(r2.PEXPIREAT_CMD, key).
			AddSeg(ttlItem.Ts, ttlItem.ExpAt.UnixMilli()).
			Build()
		return cmd, nil
	}

	// persistHint is true
	// the op should be merge or del
	ts, ok := wbTsOpt.Get()
	if !ok {
		// it may be a merge del
		parserTs, err := parser.ParseSecTs(wbEntry.Value)
		if err != nil {
			return zero, utils.Errorf("failed to parse timestamp from value: %w", err)
		}
		ts = parserTs
	}

	cmd = cbb(r2.PEXPIREAT_CMD, key).
		AddSeg(ts, 0). // zero means that the key never expires
		Build()

	return
}

func (wbc *WbConverter) convertHashSet(cbb CCB, wbTsOpt writebatch.TsOpt, wbEntry *WBE, key string) (entry.Command, error) {
	zero := entry.Command{}
	cmd := zero
	cmdSeq := wbEntry.Seq

	switch wbEntry.OpType {
	case writebatch.OpTypePut:
		{
			hashItem, err := parser.ParseHashItem(wbEntry.Key, wbEntry.Value)
			if err != nil {
				return zero, err
			}
			value, ok := hashItem.Value.Get()
			if !ok {
				err := utils.Errorf("missing value for hash item: %s, field: %s, ts: %v, seq: %d", hashItem.Key, hashItem.Field, hashItem.Ts, cmdSeq)
				return zero, err
			}
			cmd = cbb(r2.HSET_CMD, key).
				AddSeg(hashItem.Ts, hashItem.Field, value).
				Build()
			return cmd, nil
		}
	case writebatch.OpTypeDel:
		{
			ts, ok := wbTsOpt.Get()
			if !ok {
				return zero, utils.Errorf("missing timestamp for delete operation: %w", consts.ErrMissingTimestamp)
			}
			_, _, field, err := parser.ParseHashKey(wbEntry.Key)
			if err != nil {
				return zero, err
			}
			cmd = cbb(r2.HDEL_CMD, key).
				AddSeg(ts, field).
				Build()
			return cmd, nil
		}
	case writebatch.OpTypeMerge:
		{
			_, _, field, err := parser.ParseHashKey(wbEntry.Key)
			if err != nil {
				return zero, err
			}
			ts, err := parser.ParseSecTs(wbEntry.Value)
			if err != nil {
				return zero, err
			}
			cmd = cbb(r2.HDEL_CMD, key).
				AddSeg(ts, field).
				Build()
			return cmd, nil
		}
	default:
		err := utils.Errorf("unsupported writebatch operation type: %s for hash set, key: %s, seq: %d", wbEntry.OpType, key, cmdSeq)
		return zero, err
	}
}

func (wbc *WbConverter) convertZSet(cbb CCB, wbTsOpt TsOpt, wbEntry *WBE, key string) (entry.Command, error) {
	zero := entry.Command{}
	cmd := zero
	cmdSeq := wbEntry.Seq

	switch wbEntry.OpType {
	case writebatch.OpTypePut:
		zsetItem, err := parser.ParseZsetItem(wbEntry.Key, wbEntry.Value)
		if err != nil {
			return zero, err
		}
		score, ok := zsetItem.Score.Get()
		if !ok {
			return zero, utils.Errorf("missing score for zset item: %s", zsetItem.Key)
		}
		scoreStr := score.String()
		cmd = cbb(r2.ZADD_CMD, key).
			AddSeg(zsetItem.Ts, scoreStr, zsetItem.Member).
			Build()

		return cmd, nil
	case writebatch.OpTypeDel:
		ts, ok := wbTsOpt.Get()
		if !ok {
			return zero, utils.Errorf("missing timestamp for delete operation: %w", consts.ErrMissingTimestamp)
		}
		_, _, member, err := parser.ParseZsetKey(wbEntry.Key)
		if err != nil {
			return zero, err
		}
		cmd = cbb(r2.ZREM_CMD, key).
			AddSeg(ts, member).
			Build()
		return cmd, nil
	case writebatch.OpTypeMerge:
		_, _, member, err := parser.ParseZsetKey(wbEntry.Key)
		if err != nil {
			return zero, err
		}
		ts, err := parser.ParseSecTs(wbEntry.Value)
		if err != nil {
			return zero, utils.Errorf("failed to parse timestamp from value: %w", err)
		}
		cmd = cbb(r2.ZREM_CMD, key).
			AddSeg(ts, member).
			Build()
		return cmd, nil
	}

	err := utils.Errorf("unsupported writebatch operation type: %s for zset, key: %s, seq: %d", wbEntry.OpType, key, cmdSeq)
	logger.Error(err)
	return zero, err
}

func (wbc *WbConverter) convertSet(cbb CCB, wbTsOpt TsOpt, wbEntry *WBE, key string) (entry.Command, error) {
	zero := entry.Command{}
	cmd := zero
	cmdSeq := wbEntry.Seq

	switch wbEntry.OpType {
	case writebatch.OpTypePut:
		setItem, err := parser.ParseSetItem(wbEntry.Key, wbEntry.Value)
		if err != nil {
			return zero, err
		}
		cmd = cbb(r2.SADD_CMD, key).
			AddSeg(setItem.Ts, setItem.Member).
			Build()
		return cmd, nil
	case writebatch.OpTypeDel:
		ts, ok := wbTsOpt.Get()
		if !ok {
			return zero, utils.Errorf("missing timestamp for delete operation: %w", consts.ErrMissingTimestamp)
		}
		_, _, member, err := parser.ParseSetKey(wbEntry.Key)
		if err != nil {
			return zero, err
		}
		cmd = cbb(r2.SREM_CMD, key).
			AddSeg(ts, member).
			Build()
		return cmd, nil
	case writebatch.OpTypeMerge:
		_, _, member, err := parser.ParseSetKey(wbEntry.Key)
		if err != nil {
			return zero, err
		}
		ts, err := parser.ParseSecTs(wbEntry.Value)
		if err != nil {
			return zero, utils.Errorf("failed to parse timestamp from value: %w", err)
		}
		cmd = cbb(r2.SREM_CMD, key).
			AddSeg(ts, member).
			Build()
		return cmd, nil
	default:
		err := utils.Errorf("unsupported writebatch operation type: %s for set, key: %s, seq: %d", wbEntry.OpType, key, cmdSeq)
		return zero, err
	}
}

func (wbc *WbConverter) convertList(logNum int64, tsOpt TsOpt, wbSeq uint64, hints []writebatch.WriteBatchHint, listEntries []WBE) (EntryOpt, error) {
	var listType listType
	isListMeta := func(e WBE) bool {
		if parser.IsListMetaKey(e.Key) {
			listType = listTypeList
			return true
		}
		if parser.IsQueueMetaKey(e.Key) {
			listType = listTypeQueue
			return true
		}
		return false
	}

	metaWbes, wbes := utils.Partition(listEntries, isListMeta)
	if len(metaWbes) != 1 {
		return NoneEntry(), utils.Errorf("expected exactly one list meta entry, got %+v", metaWbes)
	}

	metaWbe := metaWbes[0]
	_, keyBytes, _, err := parser.ParseKey(metaWbe.Key)
	if err != nil {
		return NoneEntry(), utils.Errorf("failed to parse list meta key: %w", err)
	}

	key := string(keyBytes)

	switch listType {
	case listTypeList:
		if wbc.useQueue {
			logger.Debugf("skip list decode for key %s, use queue is true", key)
			return NoneEntry(), nil
		}
	case listTypeQueue:
		if !wbc.useQueue {
			logger.Debugf("skip queue decode for key %s, use queue is false", key)
			return NoneEntry(), nil
		}
	default:
		logger.Debugf("unsupported list type for key %s, listType: %d", key, listType)
		return NoneEntry(), nil
	}

	entry := entry.NewEntryWithSeq(key, wbSeq, uint64(logNum))
	cbb := cmdbuilder.NewCmdBuilderBuilder(wbc.serverID, wbSeq, wbc.hlcConverter)

	switch metaWbe.OpType {
	case writebatch.OpTypeDel:
		if len(hints) != 0 {
			logger.Warnf("list delete operation with hints is not supported, key: %s, seq: %d", key, wbSeq)
		}

		ts, ok := tsOpt.Get()
		if !ok {
			return NoneEntry(), utils.Errorf("missing timestamp for list delete operation: %w", consts.ErrMissingTimestamp)
		}
		cmd := cbb(r2.DEL_CMD, key).
			AddSeg(ts).
			Build()
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	case writebatch.OpTypeMerge:
		if len(hints) != 0 {
			logger.Warnf("list merge operation with hints is not supported, key: %s, seq: %d", key, wbSeq)
		}

		ts, err := parser.ParseSecTs(metaWbe.Value)
		if err != nil {
			return NoneEntry(), utils.Errorf("failed to parse timestamp from value: %w", err)
		}
		cmd := cbb(r2.DEL_CMD, key).
			AddSeg(ts).
			Build()
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	}

	if len(hints) != 1 {
		err := utils.Errorf("expected exactly one list hint, got %d", len(hints))
		return NoneEntry(), err
	}

	itemParse := makeListItemParseFunc(listType)
	hint := hints[0]

	switch hint.Type() {
	case writebatch.WriteBatchHintTypeLpop:
		if len(wbes) != 1 {
			return NoneEntry(), utils.Errorf("expected exactly one entry for lpop, got %d", len(wbes))
		}
		hint := hint.(*writebatch.LPopHint)
		cmd := cbb(r2.LREM_CMD, key).
			AddSeg(hint.Ts, 1, hint.RemovedValue).
			Build()
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	case writebatch.WriteBatchHintTypeRpop:
		if len(wbes) != 1 {
			return NoneEntry(), utils.Errorf("expected exactly one entry for rpop, got %d", len(wbes))
		}
		hint := hint.(*writebatch.RPopHint)
		cmd := cbb(r2.LREM_CMD, key).
			AddSeg(hint.Ts, -1, hint.RemovedValue).
			Build()
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	case writebatch.WriteBatchHintTypeLpush:
		hint := hint.(*writebatch.LPushHint)
		if len(wbes) != int(hint.Cnt) {
			return NoneEntry(), utils.Errorf("expected %d entries for lpush, got %d", hint.Cnt, len(wbes))
		}
		cb := cbb(r2.LPUSH_CMD, key)
		cmd, err := handleListPush(cb, itemParse, wbes)
		if err != nil {
			return NoneEntry(), err
		}
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	case writebatch.WriteBatchHintTypeRpush:
		hint := hint.(*writebatch.RPushHint)
		if len(wbes) != int(hint.Cnt) {
			return NoneEntry(), utils.Errorf("expected %d entries for rpush, got %d", hint.Cnt, len(wbes))
		}
		cb := cbb(r2.RPUSH_CMD, key)
		cmd, err := handleListPush(cb, itemParse, wbes)
		if err != nil {
			return NoneEntry(), err
		}
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	case writebatch.WriteBatchHintTypeLtrim:
		{
			hint := hint.(*writebatch.LTrimHint)
			if len(wbes) != len(hint.RemovedValues) {
				return NoneEntry(), utils.Errorf("expected %d entries for ltrim, got %d entries", len(hint.RemovedValues), len(wbes))
			}

			for _, val := range hint.RemovedValues {

				args := []any{hint.Direction}
				args = append(args, val)

				cmd := cbb(r2.LREM_CMD, key).
					AddSeg(hint.Ts, args...).
					Build()

				entry.AddCmd(cmd)
			}

			return SomeEntry(entry), nil
		}
	case writebatch.WriteBatchHintTypeLsetNew:
		if len(wbes) != 1 {
			return NoneEntry(), utils.Errorf("expected exactly one entry for lset new, got %d", len(wbes))
		}
		wbEntry := wbes[0]
		queItem, err := parser.ParseQueueItem(wbEntry.Key, wbEntry.Value)
		if err != nil {
			return NoneEntry(), err
		}
		if queItem.Value.IsAbsent() {
			return NoneEntry(), utils.Errorf("no value to set for lset new, key: %s, seq: %d", queItem.Key, wbEntry.Seq)
		}
		cb := cbb(r2.LSET_CMD, key)
		cmd := handleListNewSet(cb, queItem)
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil

	case writebatch.WriteBatchHintTypeLsetOld:
		if len(wbes) != 1 {
			return NoneEntry(), utils.Errorf("expected exactly one entry for lset old, got %d", len(wbes))
		}
		wbEntry := wbes[0]
		listItem, err := parser.ParseListItem(wbEntry.Key, wbEntry.Value)
		if err != nil {
			return NoneEntry(), err
		}
		if listItem.Value.IsAbsent() {
			return NoneEntry(), utils.Errorf("no value to set for lset old, key: %s, seq: %d", listItem.Key, wbEntry.Seq)
		}
		hint := hint.(*writebatch.LSetOldHint)
		index := int32(hint.Index)
		kvrocksIndex := KVROCKS_LIST_ORIGIN
		if index >= 0 {
			kvrocksIndex += uint64(index)
		} else {
			kvrocksIndex -= uint64(-index)
		}
		cmd := cbb(r2.LSET_CMD, key).
			AddSeg(listItem.Ts, kvrocksIndex, listItem.Value.MustGet()).
			Build()
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	case writebatch.WriteBatchHintTypeLrem:
		hint := hint.(*writebatch.LRemHint)

		args := []any{}
		args = append(args, hint.Cnt, hint.RemovedValue)

		cmd := cbb(r2.LREM_CMD, key).
			AddSeg(hint.Ts, args...).
			Build()
		entry.AddCmd(cmd)
		return SomeEntry(entry), nil
	default:
		err := utils.Errorf("unsupported list hint type: %s, key: %s, seq: %d", hint.Type(), key, wbSeq)
		logger.Error(err)
		return NoneEntry(), err
	}
}

func handleListNewSet(cb *CB, queItem *parser.QueueItem) entry.Command {
	logger.Debugf("handling list new set for queue item: %+v", queItem)
	var origin1 uint32 = math.MaxUint32 / 2
	var origin2 uint32 = math.MaxUint32 - 10

	index := queItem.Seq

	dist1 := utils.AbsDiff(index, origin1)
	dist2 := utils.AbsDiff(index, origin2)

	origin := origin1
	if dist2 < dist1 {
		origin = origin2
	}
	kvrocksIndex := KVROCKS_LIST_ORIGIN + uint64(index) - uint64(origin) - 1

	logger.Debugf("queue item: %+v, origin1: %d, origin2: %d, dist1: %d, dist2: %d, index: %d, kvrocksIndex: %d",
		queItem, origin1, origin2, dist1, dist2, index, kvrocksIndex)

	cb.AddSeg(
		queItem.Ts,
		kvrocksIndex,
		queItem.Value.MustGet(),
	)

	return cb.Build()
}

func handleListPush(cb *CB, parseFunc parser.ListItemParseFunc, wbEntries []writebatch.WriteBatchEntry) (entry.Command, error) {
	var zero entry.Command
	listVals := make([]parser.ListVal, 0, len(wbEntries))
	for _, wbEntry := range wbEntries {
		if wbEntry.OpType != writebatch.OpTypePut {
			logger.Warnf("unexpected writebatch entry op type: %s, expected %s for list push, key: %s, seq: %d",
				wbEntry.OpType, writebatch.OpTypePut, string(wbEntry.Key), wbEntry.Seq)
			continue
		}
		listVal, err := parseFunc(wbEntry.Key, wbEntry.Value)
		if err != nil {
			return zero, utils.Errorf("failed to parse list value: %w", err)
		}
		if listVal.GetVal().IsAbsent() {
			continue
		}
		listVals = append(listVals, listVal)
	}

	if len(listVals) == 0 {
		logger.Errorf("no valid list values found for push, key: %s, seq: %d", string(wbEntries[0].Key), wbEntries[0].Seq)
		return zero, nil
	}

	for _, listVal := range listVals {
		cb.AddSeg(listVal.GetTs(), listVal.GetVal().MustGet())
	}

	return cb.Build(), nil
}

func makeListItemParseFunc(listType listType) parser.ListItemParseFunc {
	switch listType {
	case listTypeList:
		return func(k, v []byte) (parser.ListVal, error) {
			return parser.ParseListItem(k, v)
		}
	case listTypeQueue:
		return func(k, v []byte) (parser.ListVal, error) {
			return parser.ParseQueueItem(k, v)
		}
	default:
		panic("unsupported list type: " + string(listType))
	}
}
