package psyncreader

import (
	"context"
	"strings"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type EventType string

const (
	EventTypeApplyBatch EventType = "pr_et_apply_batch" // psync reader - event type - ApplyBatch
	EventTypePing       EventType = "pr_et_ping"        // psync reader - event type - Ping
)

type Event struct {
	Type    EventType
	Payload any // AppyBatchCommand or nil
}

func StartReadPsync(ctx context.Context, reader *RespReader, eventChan chan<- Event) error {
	stop := false
	go func() {
		<-ctx.Done()
		stop = true
		logger.Info("psync reader context cancelled, stopping read loop")
	}()

	if err := readPsyncReader(ctx, &stop, reader, eventChan); err != nil {
		if err == ctx.Err() {
			return nil
		}
		return utils.Errorf("failed to read psync: %w", err)
	}

	logger.Infof("psync reader stopped reading, context cancelled: %v", ctx.Err())
	return nil
}

func readPsyncReader(ctx context.Context, stop *bool, reader *RespReader, event<PERSON>han chan<- Event) error {
	for !(*stop) {
		resp, err := reader.ReadRespObject()
		if err != nil {
			return err
		}

		switch resp.Type {
		case Array, SimpleString:
			bc, err := BaseCommandFromResp(resp)
			if err != nil {
				return err
			}
			event, err := eventFromBaseCommand(bc)
			if err != nil {
				return err
			}

			if err := utils.SendWithContext(ctx, eventChan, event); err != nil {
				return err
			}

		case Error, Integer, BulkString:
			return utils.Errorf("unexpected RESP %v type %s, expected Array", resp, RespType2String(resp.Type))
		default:
			return utils.Errorf("unknown RESP type %s", RespType2String(resp.Type))
		}
	}

	return nil
}

func eventFromBaseCommand(cmd BaseCommand) (Event, error) {
	switch strings.ToLower(strings.TrimSpace(cmd.Cmd)) {
	case "applybatch2":
		applyCmd, err := ApplyBatchCommandFromBaseCommand(cmd)
		if err != nil {
			return Event{}, err
		}

		return Event{
			Type:    EventTypeApplyBatch,
			Payload: applyCmd,
		}, nil
	case "ping":
		return Event{
			Type:    EventTypePing,
			Payload: nil,
		}, nil
	default:
		return Event{}, utils.Errorf("unknown command: %s", cmd.Cmd)
	}
}
