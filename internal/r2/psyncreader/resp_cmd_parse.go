package psyncreader

import (
	"strconv"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/writebatch"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type BaseCommand struct {
	Cmd  string
	Args []RespObject
}

func BaseCommandFromResp(obj RespObject) (BaseCommand, error) {
	parseArray := func(obj RespObject) (BaseCommand, error) {
		array, _ := obj.GetArray()
		if len(array) < 1 {
			return BaseCommand{}, utils.Errorf("expected at least one element in array, got %d", len(array))
		}
		rawCmd := array[0]
		cmd := ""

		switch rawCmd.Type {
		case SimpleString, BulkString:
			cmd, _ = rawCmd.GetString()
		case Error:
			err, _ := rawCmd.GetError()
			return BaseCommand{}, utils.Errorf("expected SimpleString or BulkString type for command, got Error: %w", err)
		default:
			return BaseCommand{}, utils.Errorf("expected SimpleString or BulkString type for command, got %s", RespType2String(rawCmd.Type))
		}

		return BaseCommand{
			Cmd:  cmd,
			Args: array[1:],
		}, nil
	}

	switch obj.Type {
	case SimpleString, BulkString:
		str, _ := obj.GetString()
		return BaseCommand{
			Cmd:  str,
			Args: nil,
		}, nil
	case Error:
		err, _ := obj.GetError()
		return BaseCommand{}, utils.Errorf("expected SimpleString or BulkString type for command, got Error: %w", err)
	case Array:
		return parseArray(obj)
	default:
		return BaseCommand{}, utils.Errorf("expected SimpleString, BulkString, or Array type for command, got %s: %v", RespType2String(obj.Type), obj)
	}
}

type ApplyBatchCommand struct {
	TableName  string
	LogNum     int64
	Seq        uint64
	WriteBatch []byte
}

// applybatch2 <tablename> <log_num> <seq> <write_batch>
func ApplyBatchCommandFromBaseCommand(cmd BaseCommand) (*ApplyBatchCommand, error) {
	if cmd.Cmd != "applybatch2" {
		return nil, utils.Errorf("expected command 'applybatch2', got '%s'", cmd.Cmd)
	}

	if len(cmd.Args) != 4 {
		return nil, utils.Errorf("expected 3 arguments for 'applybatch2', got %d", len(cmd.Args))
	}

	tableName, ok := cmd.Args[0].GetString()
	if !ok {
		return nil, utils.Errorf("expected first argument to be a string, got %s", RespType2String(cmd.Args[0].Type))
	}

	logNum, err := getInt(cmd.Args[1])
	if err != nil {
		return nil, utils.Errorf("failed to get log num: %w", err)
	}

	seq, err := getInt(cmd.Args[2])
	if err != nil {
		return nil, utils.Errorf("failed to get sequence number: %w", err)
	}

	writeBatch, ok := cmd.Args[3].GetBytes()
	if !ok {
		return nil, utils.Errorf("expected fourth argument to be bytes, got %s", RespType2String(cmd.Args[3].Type))
	}

	return &ApplyBatchCommand{
		TableName:  tableName,
		LogNum:     logNum,
		Seq:        uint64(seq),
		WriteBatch: writeBatch,
	}, nil
}

func (cmd *ApplyBatchCommand) ToWriteBatch() (*writebatch.WriteBatch, error) {
	wb, err := writebatch.ParseWriteBatch(cmd.WriteBatch)
	if err != nil {
		return nil, err
	}
	return wb, nil
}

func getInt(obj RespObject) (int64, error) {
	switch obj.Type {
	case Integer:
		return obj.Value.(int64), nil
	case SimpleString:
	case BulkString:
		str, _ := obj.GetString()
		num, err := strconv.ParseInt(str, 10, 64)
		return num, err
	case Error:
		return 0, utils.Errorf("expected Integer type for command argument, got Error: %v", obj.Value)
	default:
		return 0, utils.Errorf("expected Integer type for command argument, got %s: %v", RespType2String(obj.Type), obj.Value)
	}
	panic("unreachable code") // This line should never be reached, but is here to satisfy the compiler
}
