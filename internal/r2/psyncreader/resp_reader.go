package psyncreader

import (
	"bufio"
	"errors"
	"fmt"
	"io"
	"strconv"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/client"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type RespType byte

const (
	SimpleString RespType = '+'
	Error        RespType = '-'
	Integer      RespType = ':'
	BulkString   RespType = '$'
	Array        RespType = '*'
)

func RespType2String(t RespType) string {
	switch t {
	case SimpleString:
		return "SimpleString"
	case Error:
		return "Error"
	case Integer:
		return "Integer"
	case BulkString:
		return "BulkString"
	case Array:
		return "Array"
	default:
		return fmt.Sprintf("Unknown(%d)", t)
	}
}

type RespObject struct {
	Type  RespType
	Value any
}

func (ro *RespObject) GetArray() ([]RespObject, bool) {
	if ro.Type != Array {
		return nil, false
	}
	arr, ok := ro.Value.([]RespObject)
	return arr, ok
}

func (ro *RespObject) GetString() (string, bool) {
	switch ro.Type {
	case SimpleString:
		str, ok := ro.Value.(string)
		return str, ok
	case BulkString:
		bytes, ok := ro.Value.([]byte)
		if !ok {
			return "", false
		}
		return string(bytes), true
	}
	return "", false
}

func (ro *RespObject) GetBytes() ([]byte, bool) {
	switch ro.Type {
	case SimpleString:
		str, ok := ro.Value.(string)
		if !ok {
			return nil, false
		}
		return []byte(str), true
	case BulkString:
		bytes, ok := ro.Value.([]byte)
		return bytes, ok
	}
	return nil, false
}

func (ro *RespObject) GetError() (error, bool) {
	if ro.Type != Error {
		return nil, false
	}
	err, ok := ro.Value.(error)
	return err, ok
}

func (ro *RespObject) GetInt() (int64, bool) {
	if ro.Type != Integer {
		return 0, false
	}
	num, ok := ro.Value.(int64)
	return num, ok
}

type RespReader struct {
	*bufio.Reader
	cli *client.Redis
}

func NewRespReaderFromClient(cli *client.Redis) *RespReader {
	return &RespReader{
		Reader: bufio.NewReader(cli.GetBufioReader()),
		cli:    cli,
	}
}

func (rr *RespReader) Close() {
	if rr.cli != nil {
		rr.cli.Close()
		rr.cli = nil
	}
}

func (rr *RespReader) readLine() ([]byte, error) {
	var line []byte
	for {
		chunk, isPrefix, err := rr.ReadLine()
		if err != nil {
			return nil, err
		}

		if line == nil {
			line = chunk
		} else {

			line = append(line, chunk...)
		}
		if !isPrefix {
			break
		}
	}

	if len(line) < 2 {
		return nil, utils.Errorf("invalid RESP line: %s: %w", line, io.ErrUnexpectedEOF)
	}

	return line, nil
}

func (rr *RespReader) ReadRespObject() (RespObject, error) {
	zero := RespObject{}
	peek, err := rr.Peek(1)
	if err != nil {
		return zero, err
	}

	switch RespType(peek[0]) {
	case SimpleString:
		return rr.readSimpleString()
	case Error:
		return rr.readError()
	case Integer:
		return rr.readInteger()
	case BulkString:
		return rr.readBulkString()
	case Array:
		return rr.readArray()
	default:
		return zero, utils.Errorf("unknown RESP type: %s", string(peek[0]))
	}
}

func (rr *RespReader) readSimpleString() (RespObject, error) {
	zero := RespObject{}

	line, err := rr.readLine()
	if err != nil {
		return zero, err
	}

	return RespObject{
		Type:  SimpleString,
		Value: string(line[1:]), // strip the leading '+'
	}, nil
}

func (rr *RespReader) readError() (RespObject, error) {
	zero := RespObject{}

	line, err := rr.readLine()
	if err != nil {
		return zero, err
	}

	return RespObject{
		Type:  Error,
		Value: errors.New(string(line)),
	}, nil
}

func (rr *RespReader) readInteger() (RespObject, error) {
	zero := RespObject{}

	line, err := rr.readLine()
	if err != nil {
		return zero, err
	}

	// strip the leading ':'
	numStr := string(line[1:])
	num, err := strconv.ParseInt(numStr, 10, 64)
	if err != nil {
		return zero, utils.Errorf("failed to parse integer: %w", err)
	}

	return RespObject{
		Type:  Integer,
		Value: num,
	}, nil
}

func (rr *RespReader) readBulkString() (RespObject, error) {
	zero := RespObject{}

	line, err := rr.readLine()
	if err != nil {
		return zero, err
	}

	if len(line) < 2 {
		return zero, utils.Errorf("invalid bulk string length: %s", line)
	}

	length, err := strconv.Atoi(string(line[1:]))
	if err != nil {
		return zero, utils.Errorf("failed to parse bulk string length: %w", err)
	}

	data := make([]byte, length)
	if _, err := io.ReadFull(rr, data); err != nil {
		return zero, utils.Errorf("failed to read bulk string data: %w", err)
	}

	if _, err := rr.Discard(2); err != nil {
		return zero, utils.Errorf("failed to discard trailing CRLF: %w", err)
	}

	return RespObject{
		Type:  BulkString,
		Value: data,
	}, nil
}

func (rr *RespReader) readArray() (RespObject, error) {
	zero := RespObject{}

	line, err := rr.readLine()
	if err != nil {
		return zero, err
	}

	// the line looks like "*<length>", the length has to be at least 1 character
	if len(line) < 2 {
		return zero, utils.Errorf("invalid array length: %s", line)
	}

	length, err := strconv.Atoi(string(line[1:]))
	if err != nil {
		return zero, utils.Errorf("failed to parse array length: %w", err)
	}

	if length < 0 {
		return RespObject{
			Type:  Array,
			Value: nil, // nil for empty array
		}, nil
	}

	elements := make([]RespObject, 0, length)
	for range length {
		obj, err := rr.ReadRespObject()
		if err != nil {
			return zero, utils.Errorf("failed to read array element: %w", err)
		}

		elements = append(elements, obj)
	}

	return RespObject{
		Type:  Array,
		Value: elements,
	}, nil
}
