package mergeoperator

import (
	"bytes"
	"slices"
	"strconv"
	"time"

	"git.xiaojukeji.com/foundation/fusion/gorocksdb"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
)

var gCounterPrefix = []byte("fusion:aof:sc")

type MergeOperator struct{}

// impl gorocksdb.MergeOperator for MergeOperator
var _ gorocksdb.MergeOperator = (*MergeOperator)(nil)

func (*MergeOperator) Name() string {
	return "RocksGetAndSetMergeOperator"
}

func (op *MergeOperator) FullMerge(key, existingValue []byte, operands [][]byte) ([]byte, bool) {
	operandLen := len(operands)
	if operandLen == 0 {
		return existingValue, true
	}

	// sort operands by timestamp, ** Reverse **
	operands = append(operands, existingValue)
	slices.SortFunc(operands, op.makeSortByTs(true))

	// For the most cases, it's sufficient to return the latest value
	if !op.isCounterOp(operands[0]) {
		return operands[0], true
	}

	type Delta struct {
		ts    time.Time
		delta int64
	}

	var baseValIndex = len(operands) - 1
	seenUids := make(map[string]struct{})
	deltas := []Delta{}

	for i, operand := range operands {
		if !op.isCounterOp(operand) {
			baseValIndex = i
			break
		}

		// now it must be a delta operand
		val, ts, _, err := parser.ParseValue(operand)
		if err != nil {
			continue // skip invalid operand
		}

		delta, uid := op.parseCounterDelta(val.OrEmpty())
		if _, exists := seenUids[uid]; exists {
			continue // skip dup
		}

		deltas = append(deltas, Delta{ts: ts, delta: delta})
		seenUids[uid] = struct{}{}
	}

	// var baseNum int64 = 0
	// var ttl time.Time = makeFarFuture()

	baseNum, newTs, ttl := op.parseBaseCntVal(operands[baseValIndex])
	delta := int64(0)

	for _, deltaOp := range deltas {
		if deltaOp.ts.Before(ttl) {
			continue //skip expired deltas
		}
		delta += deltaOp.delta

		if newTs.Before(deltaOp.ts) {
			newTs = deltaOp.ts // update the timestamp to the latest
		}
	}

	cntNum := strconv.FormatInt(baseNum+delta, 10)
	return buildVal(cntNum, newTs, ttl), true
}

func (op *MergeOperator) parseBaseCntVal(rawValue []byte) (num int64, ts, ttl time.Time) {
	val, ts, ttlOpt, err := parser.ParseValue(rawValue)
	if err != nil {
		return
	}

	ttl = ttlOpt.OrEmpty()

	if ttlOpt.OrElse(makeFarFuture()).Before(time.Now()) {
		// the value is expired
		return
	}

	// If this fails, we'll get 0,
	// which is exactly what we need in case of an error.
	num, _ = strconv.ParseInt(string(val.OrElse([]byte("0"))), 10, 64)
	return
}

// return (ts, ttl)
func (op *MergeOperator) tryParseTsAndTtl(value []byte) (time.Time, time.Time) {
	zero := time.Time{}
	farFuture := makeFarFuture()

	_, ts, ttlOpt, err := parser.ParseValue(value)
	if err != nil {
		return zero, farFuture
	}

	if ttl, ok := ttlOpt.Get(); ok {
		return ts, ttl
	}

	return ts, farFuture
}

func (op *MergeOperator) isCounterOp(value []byte) bool {
	return bytes.HasPrefix(value, gCounterPrefix)
}

func (op *MergeOperator) makeSortByTs(reverse bool) func(v1, v2 []byte) int {
	return func(v1, v2 []byte) int {
		if reverse {
			v1, v2 = v2, v1 // reverse the order
		}

		ts1, _ := op.tryParseTsAndTtl(v1)
		ts2, _ := op.tryParseTsAndTtl(v2)
		if ts1.Before(ts2) {
			return -1
		}
		if ts2.Before(ts1) {
			return 1
		}
		return 0
	}
}

func (op *MergeOperator) parseCounterDelta(rawValue []byte) (delta int64, uid string) {
	rawValue = rawValue[len(gCounterPrefix):] // remove the prefix

	// operand belikes to the format
	// "{uid}:{delta}\0"

	splitPos := bytes.LastIndexByte(rawValue, ':')
	if splitPos < 0 {
		return 0, "invalid"
	}

	uid = string(rawValue[:splitPos]) // uid is a string

	// remove the trailling '\0'
	deltaRaw := rawValue[splitPos+1 : len(rawValue)-1]
	delta, err := strconv.ParseInt(string(deltaRaw), 10, 64)
	if err != nil {
		return 0, "invalid"
	}

	return delta, uid
}

func makeFarFuture() time.Time {
	return time.Date(9999, 12, 31, 23, 59, 59, 0, time.UTC)
}

func buildVal(val string, ts time.Time, ttl time.Time) []byte {
	valBytes := []byte(val)
	tsSec := ts.Unix()
	valBytes = append(valBytes, strconv.FormatInt(tsSec, 10)...)

	if !ttl.IsZero() {
		ttlUs := ttl.UnixMicro()
		valBytes = append(valBytes, strconv.FormatInt(ttlUs, 10)...)
		valBytes = append(valBytes, r2.VALUE_TERMINATOR)
	}

	return valBytes
}
