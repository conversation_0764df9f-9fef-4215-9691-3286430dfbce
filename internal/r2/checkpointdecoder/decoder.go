package checkpointdecoder

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	pseudohlc "git.xiaojukeji.com/fusion/fusion-syncer/internal/pseudo_hlc"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/checkpoint"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/cmdbuilder"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

type CheckpointDecoder struct {
	DB   *checkpoint.CheckPoint
	Slot int32

	ServerID  string
	Seq       uint64
	BatchSize int
	TableName string
	UseQueue  bool

	hlcConverter pseudohlc.HlcConverter
}

func NewCheckpointDecoder(db *checkpoint.CheckPoint, useQueue bool, slot int32, serverID string, seq uint64, batchSize int, tableName string, hlcConverter pseudohlc.HlcConverter) (*CheckpointDecoder, error) {
	if slot < r2.MIN_R2_SLOT || slot > r2.MAX_R2_SLOT {
		return nil, utils.Errorf("invalid slot: %d, must be in range [%d, %d]", slot, r2.MIN_R2_SLOT, r2.MAX_R2_SLOT)
	}

	return &CheckpointDecoder{
		DB:           db,
		Slot:         slot,
		ServerID:     serverID,
		Seq:          seq,
		BatchSize:    batchSize,
		TableName:    tableName,
		UseQueue:     useQueue,
		hlcConverter: hlcConverter,
	}, nil
}

func (d *CheckpointDecoder) String() string {
	return fmt.Sprintf("CheckpointDecoder{DB: %s, Slot: %d, ServerID: %s, Seq: %d, BatchSize: %d}",
		d.DB.String(), d.Slot, d.ServerID, d.Seq, d.BatchSize)
}

func (d *CheckpointDecoder) newCmdBuilder(cmd, key string) *cmdbuilder.CmdBuilder {
	return cmdbuilder.NewCmdBuilder(d.ServerID, d.Seq, cmd, key, d.hlcConverter)
}

func (cpec *CheckpointDecoder) delCmd(key string, ts time.Time) entry.Command {
	cmdBuilder := cpec.newCmdBuilder(r2.DEL_CMD, key)
	cmdBuilder.AddSegs(cmdbuilder.NewCmdSeg(ts))
	return cmdBuilder.Build()
}

// pexpire
func (cpec *CheckpointDecoder) expireCmd(key string, expireAt time.Time, ts time.Time) entry.Command {
	cmdBuilder := cpec.newCmdBuilder(r2.PEXPIREAT_CMD, key)
	cmdBuilder.AddSegs(cmdbuilder.NewCmdSeg(ts, expireAt.UnixMilli()))
	return cmdBuilder.Build()
}

// del and set with ttl (if ttl is not empty)
func (cpec *CheckpointDecoder) string2Cmd(str *parser.StringItem) []entry.Command {
	cmds := make([]entry.Command, 0, 3)

	delCmd := cpec.delCmd(string(str.Key), str.Ts)
	key := string(str.Key)

	strVal := str.Value.MustGet()

	setCmd := cpec.newCmdBuilder(r2.SET_CMD, key).
		AddSegs(cmdbuilder.NewCmdSeg(str.Ts, strVal)).
		Build()

	cmds = append(cmds, delCmd, setCmd)

	if ttl, ok := str.Ttl.Get(); ok {
		// If ttl is present, add expire command
		// make the expire command a bit later than the set command (100ms)
		expireCmd := cpec.expireCmd(key, ttl, str.Ts.Add(time.Millisecond*100))
		cmds = append(cmds, expireCmd)
	}

	return cmds
}

func (cpec *CheckpointDecoder) list2Cmd(key string, items utils.IteratorFunc[parser.ListVal]) []entry.Command {
	preLude := cpec.newCmdBuilder(r2.RPUSH_CMD, key)
	chunkIter := utils.Chunk(items, cpec.BatchSize)

	cmds := []entry.Command{{}}
	eariliestTs := time.Time{}

	for chunk, ok := chunkIter(); ok; chunk, ok = chunkIter() {
		cb := *preLude

		for _, item := range chunk {
			itemVal := item.GetVal().MustGet()
			itemTs := item.GetTs()
			cb.AddSegs(cmdbuilder.NewCmdSeg(itemTs, itemVal))

			if eariliestTs.IsZero() || itemTs.Before(eariliestTs) {
				eariliestTs = itemTs
			}
		}

		cmds = append(cmds, cb.Build())
	}

	if len(cmds) == 1 {
		return nil
	}

	delCmd := cpec.delCmd(key, eariliestTs.Add(-time.Second))
	// Replace the first empty command with the delete command
	cmds[0] = delCmd

	return cmds
}

func (cpec *CheckpointDecoder) set2Cmd(key string, items utils.IteratorFunc[*parser.SetItem]) []entry.Command {
	preLude := cpec.newCmdBuilder(r2.SADD_CMD, key)
	chunkIter := utils.Chunk(items, cpec.BatchSize)

	cmds := []entry.Command{{}}
	eariliestTs := time.Time{}

	for chunk, ok := chunkIter(); ok; chunk, ok = chunkIter() {
		cmd := *preLude

		for _, item := range chunk {
			cmd.AddSegs(cmdbuilder.NewCmdSeg(item.Ts, item.Member))

			if eariliestTs.IsZero() || item.Ts.Before(eariliestTs) {
				eariliestTs = item.Ts
			}
		}

		cmds = append(cmds, cmd.Build())
	}

	if len(cmds) == 1 {
		return nil
	}

	delCmd := cpec.delCmd(key, eariliestTs.Add(-time.Second))
	cmds[0] = delCmd
	return cmds
}

func (cpec *CheckpointDecoder) hash2Cmd(key string, items utils.IteratorFunc[*parser.HashItem]) []entry.Command {
	preLude := cpec.newCmdBuilder(r2.HSET_CMD, key)
	chunkIter := utils.Chunk(items, cpec.BatchSize)

	cmds := []entry.Command{{}}
	eariliestTs := time.Time{}

	for chunk, ok := chunkIter(); ok; chunk, ok = chunkIter() {
		cmd := *preLude

		for _, item := range chunk {
			cmd.AddSegs(cmdbuilder.NewCmdSeg(item.Ts, item.Field, item.Value.MustGet()))
			if eariliestTs.IsZero() || item.Ts.Before(eariliestTs) {
				eariliestTs = item.Ts
			}
		}

		cmds = append(cmds, cmd.Build())
	}

	if len(cmds) == 1 {
		return nil
	}

	delCmd := cpec.delCmd(key, eariliestTs.Add(-time.Second))
	cmds[0] = delCmd

	return cmds
}

func (cpec *CheckpointDecoder) zset2Cmd(key string, items utils.IteratorFunc[*parser.ZSetItem]) []entry.Command {
	preLude := cpec.newCmdBuilder(r2.ZADD_CMD, key)
	chunkIter := utils.Chunk(items, cpec.BatchSize)

	cmds := []entry.Command{{}}
	eariliestTs := time.Time{}

	for chunk, ok := chunkIter(); ok; chunk, ok = chunkIter() {
		cmd := *preLude

		for _, item := range chunk {
			score := item.Score.MustGet().String()
			cmd.AddSegs(cmdbuilder.NewCmdSeg(item.Ts, score, item.Member))

			if eariliestTs.IsZero() || item.Ts.Before(eariliestTs) {
				eariliestTs = item.Ts
			}
		}

		cmds = append(cmds, cmd.Build())
	}

	if len(cmds) == 1 {
		return nil
	}

	delCmd := cpec.delCmd(key, eariliestTs.Add(-time.Second))
	cmds[0] = delCmd

	return cmds
}

func (cpec *CheckpointDecoder) ttl2Cmd(key string, ttlItem *parser.TTLItem) []entry.Command {
	expireAt := ttlItem.ExpAt
	if expireAt.IsZero() {
		logger.Warnf("invalid TTL item for key %s: expireAt is zero, ttl: %+v", key, ttlItem)
		return nil // Invalid TTL
	}

	cmd := cpec.expireCmd(key, expireAt, ttlItem.Ts)
	return []entry.Command{cmd}
}

func (cpec *CheckpointDecoder) ttlDecodeRoutine(ctx context.Context, outputCh chan<- *entry.Entry) error {
	iter, err := cpec.DB.GetTTLIter(cpec.Slot)
	if err != nil {
		return err
	}
	defer iter.Close()
	for iter.Valid() {
		if err := ctx.Err(); err != nil {
			return err
		}

		ttlItem, err := parser.ParseTTLItem(iter.Key().Data(), iter.Value().Data())
		if err != nil {
			return nil
		}

		key := string(ttlItem.Key)

		cmds := cpec.ttl2Cmd(key, ttlItem)
		if cmds == nil {
			continue
		}

		entry := entry.NewEntry(key)
		entry.AddPayload(cmds...)
		if err := utils.SendWithContext(ctx, outputCh, entry); err != nil {
			return err
		}
	}

	if err := iter.Err(); err != nil {
		return utils.Errorf("error iterating over TTL items: %w", err)
	}

	return nil
}

func (cpec *CheckpointDecoder) strDecodeRoutine(ctx context.Context, outputCh chan<- *entry.Entry) error {
	iter, err := cpec.DB.GetStrIter(cpec.Slot)
	if err != nil {
		return err
	}
	defer iter.Close()

	parse := func(rawKey, rawValue []byte) (*entry.Entry, error) {
		strItem, err := parser.ParseStringItem(rawKey, rawValue)
		if err != nil {
			return nil, err
		}

		if strItem.Value.IsAbsent() {
			// If the value is absent, it indicates deletion
			// just skip
			return nil, nil
		}

		cmds := cpec.string2Cmd(strItem)
		entry := entry.NewEntry(string(strItem.Key))
		entry.AddPayload(cmds...)
		return entry, nil
	}

	for ; iter.Valid(); iter.Next() {
		if err := ctx.Err(); err != nil {
			return err
		}

		rawKey := utils.CopySlice(iter.Key().Data())
		rawValue := utils.CopySlice(iter.Value().Data())

		cmds, err := parse(rawKey, rawValue)
		if err != nil {
			return utils.Errorf("failed to parse string item: %w", err)
		}

		if cmds == nil {
			continue
		}

		if err := utils.SendWithContext(ctx, outputCh, cmds); err != nil {
			return err
		}
	}

	if err := iter.Err(); err != nil {
		return utils.Errorf("error iterating over string items: %w", err)
	}

	return nil
}

func (cpec *CheckpointDecoder) decodeRoutine(
	ctx context.Context,
	outputCh chan<- *entry.Entry,
	getIter func(int32) (*checkpoint.ManagedIter, error),
	process func(firstRawKey, key []byte) (EntryOpt, error),
	dataType string,
) error {
	iter, err := getIter(cpec.Slot)
	if err != nil {
		return err
	}
	defer iter.Close()

	for iter.Valid() {
		if err := ctx.Err(); err != nil {
			return err
		}

		rawKey := iter.Key().Data()
		_, key, _, err := parser.ParseKey(rawKey)
		if err != nil {
			return utils.Errorf("failed to parse %s key(%s): %w", dataType, string(rawKey), err)
		}

		entryOpt, err := process(rawKey, key)
		if err != nil {
			return err
		}

		if entry, exist := entryOpt.Get(); exist {
			if err := utils.SendWithContext(ctx, outputCh, entry); err != nil {
				return err
			}
		}

		cpec.seekToNextKey(iter, key)
	}

	return nil
}

func (cpec *CheckpointDecoder) listDecodeRoutine(ctx context.Context, outputCh chan<- *entry.Entry) error {
	return cpec.decodeRoutine(
		ctx,
		outputCh,
		cpec.DB.GetListIter,
		func(_firstRawKey, key []byte) (EntryOpt, error) {
			return cpec.processList(key)
		},
		"list",
	)
}

func (cpec *CheckpointDecoder) setDecodeRoutine(ctx context.Context, outputCh chan<- *entry.Entry) error {
	return cpec.decodeRoutine(
		ctx,
		outputCh,
		cpec.DB.GetSetIter,
		cpec.processSet,
		"set",
	)
}

func (cpec *CheckpointDecoder) hashDecodeRoutine(ctx context.Context, outputCh chan<- *entry.Entry) error {
	return cpec.decodeRoutine(
		ctx,
		outputCh,
		cpec.DB.GetHashSetIter,
		cpec.processHash,
		"hashset",
	)
}

func (cpec *CheckpointDecoder) zsetDecodeRoutine(ctx context.Context, outputCh chan<- *entry.Entry) error {
	return cpec.decodeRoutine(
		ctx,
		outputCh,
		cpec.DB.GetZsetIter,
		cpec.processZSet,
		"zset",
	)
}

func (cpec *CheckpointDecoder) StartDecode(ctx context.Context, outputCh chan<- *entry.Entry) error {
	var wg sync.WaitGroup
	var once sync.Once
	var firstErr error

	for _, routine := range []func(context.Context, chan<- *entry.Entry) error{
		cpec.strDecodeRoutine,
		cpec.listDecodeRoutine,
		cpec.setDecodeRoutine,
		cpec.hashDecodeRoutine,
		cpec.zsetDecodeRoutine,
	} {
		wg.Add(1)
		go func(routineFunc func(context.Context, chan<- *entry.Entry) error) {
			defer wg.Done()
			if err := routineFunc(ctx, outputCh); err != nil {
				once.Do(func() {
					firstErr = err
				})
			}
		}(routine)
	}
	wg.Wait()

	if firstErr != nil {
		logger.Errorf("checkpoint decode routine failed: %s", firstErr)
		return firstErr
	}

	firstErr = cpec.ttlDecodeRoutine(ctx, outputCh)

	if firstErr != nil {
		logger.Errorf("checkpoint ttl decode routine failed: %s", firstErr)
	}

	return firstErr
}
