package checkpointdecoder

import (
	"encoding/binary"
	"fmt"
	"strconv"
	"strings"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/checkpoint"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"github.com/samber/mo"
)

func (cpec *CheckpointDecoder) buildListKey(key []byte) []byte {
	// {slot}$^${key}&&C
	listkey := fmt.Sprintf("%d%s%s%sC", cpec.Slot, r2.SLOT_SPLITTER, key, r2.KEY_SPLITTER)
	return []byte(listkey)
}

func (cpec *CheckpointDecoder) buildQueueKey(key []byte) []byte {
	// {slot}$^${quekey}&&meta
	quekey := fmt.Sprintf("%d%s%s%smeta", cpec.Slot, r2.SLOT_SPLITTER, key, r2.KEY_SPLITTER)
	return []byte(quekey)
}

func buildQueItemKey(meta *parser.QueueMeta, index uint32) []byte {
	// {slot}$^${key}&&{seq} # with fuking little-endian
	totalLen := 4 + len(meta.Key) + len("$^$&&") + 4
	buf := make([]byte, 0, totalLen)
	buf = append(buf, []byte(strconv.Itoa(int(meta.Slot)))...)
	buf = append(buf, r2.SLOT_SPLITTER...)
	buf = append(buf, meta.Key...)
	buf = append(buf, r2.KEY_SPLITTER...)
	seq := make([]byte, 4)
	binary.LittleEndian.PutUint32(seq, index)
	buf = append(buf, seq...)
	return buf
}

func (cpec *CheckpointDecoder) buildKeyLowerBound(rawKey []byte) []byte {
	// {slot}$^${key}
	return fmt.Appendf(nil, "%d%s%s", cpec.Slot, r2.SLOT_SPLITTER, rawKey)
}

func (cpec *CheckpointDecoder) buildKeyUpperBound(rawKey []byte) []byte {
	// {slot}$^${key}{'\xff' * many}
	// Any key belike {slot}$^${key}&&
	// "&&" must less then '\xff' in bytes, so we can use '\xff' * 8 to ensure the upper bound
	key := fmt.Sprintf("%d%s%s%s%s", cpec.Slot, r2.SLOT_SPLITTER, rawKey, r2.KEY_SPLITTER, strings.Repeat("\xff", 8))
	return []byte(key)
}

func (cpec *CheckpointDecoder) seekToNextKey(iter *checkpoint.ManagedIter, curKey []byte) {
	upperBound := cpec.buildKeyUpperBound(curKey)
	iter.Seek(upperBound)
}

type EntryOpt = mo.Option[*entry.Entry]

func SomeEntry(e *entry.Entry) EntryOpt {
	return mo.Some(e)
}

func NoneEntry() EntryOpt {
	return mo.None[*entry.Entry]()
}
