package checkpointdecoder

import (
	"bytes"

	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

func (cpec *CheckpointDecoder) processList(key []byte) (entryOpt EntryOpt, err error) {
	metaData, err := cpec.getListMetadata(key)
	if err != nil {
		return
	}

	switch {
	case metaData.listMetaVal != nil:
		if cpec.UseQueue {
			logger.Debugf("skip list decode for key %s, use queue is true", string(metaData.listKey))
			return NoneEntry(), nil
		}
		return cpec.handleListDecode(metaData.listKey, *metaData.listMetaVal)

	case metaData.queMetaVal != nil:
		if cpec.UseQueue {
			return cpec.handleQueueDecode(metaData.queKey, *metaData.queMetaVal)
		}
		logger.Debugf("skip queue decode for key %s, use queue is false", string(metaData.queKey))
		return NoneEntry(), nil

	default:
		err = utils.Errorf("neither list nor queue meta found for key %s", string(key))
	}
	return
}

func (cpec *CheckpointDecoder) getListMetadata(key []byte) (*struct {
	listKey     []byte
	listMetaVal *[]byte
	queKey      []byte
	queMetaVal  *[]byte
}, error) {
	listKey := cpec.buildListKey(key)
	listMetaVal, err := cpec.DB.Get(r2.R2CF_List, listKey)
	if err != nil {
		return nil, utils.Errorf("failed to get list meta for key %s: %w", string(listKey), err)
	}

	if listMetaVal != nil {
		return &struct {
			listKey     []byte
			listMetaVal *[]byte
			queKey      []byte
			queMetaVal  *[]byte
		}{listKey: listKey, listMetaVal: listMetaVal}, nil
	}

	queKey := cpec.buildQueueKey(key)
	queMetaVal, err := cpec.DB.Get(r2.R2CF_List, queKey)
	if err != nil {
		return nil, utils.Errorf("failed to get queue meta for key %s: %w", string(queKey), err)
	}

	return &struct {
		listKey     []byte
		listMetaVal *[]byte
		queKey      []byte
		queMetaVal  *[]byte
	}{queKey: queKey, queMetaVal: queMetaVal}, nil
}

func (cpec *CheckpointDecoder) handleListDecode(rawListMetaKey, rawListMetaVal []byte) (entryOpt EntryOpt, err error) {
	listMeta, err := parser.ParseListMeta(rawListMetaKey, rawListMetaVal)
	if err != nil {
		return
	}

	if listMeta.Size == 0 {
		return
	}

	key := listMeta.Key
	keyStr := string(key)

	lowerBound := cpec.buildKeyLowerBound(key)
	upperBound := cpec.buildKeyUpperBound(key)

	iter, err := cpec.DB.NewIterator(r2.R2CF_List, upperBound)
	if err != nil {
		err = utils.Errorf("failed to create iterator for list: %w", err)
		return
	}
	defer iter.Close()
	iter.Seek(lowerBound)

	var itermIter func() (parser.ListVal, bool)
	itermIter = func() (parser.ListVal, bool) {
		if !iter.Valid() {
			return nil, false
		}

		if bytes.HasSuffix(iter.Key().Data(), parser.LIST_META_SUFFIX) {
			// it's a meta key, skip it
			iter.Next()
			return itermIter()
		}

		var listItem *parser.ListItem
		listItem, err = parser.ParseListItem(iter.Key().Data(), iter.Value().Data())
		if err != nil {
			return nil, false
		}

		val, exist := listItem.Value.Get()
		if !exist {
			// If the value is absent, it means the item has been deleted.
			// We skip this item and continue to the next one.
			iter.Next()
			return itermIter()
		}

		listItem.Key = utils.CopySlice(listItem.Key)
		listItem.Seq = utils.CopySlice(listItem.Seq)
		listItem.Value = parser.SomeBytes(utils.CopySlice(val))

		iter.Next()
		return listItem, true
	}

	cmds := cpec.list2Cmd(keyStr, itermIter)
	if err != nil {
		// the err is emitted by the itermIter function
		return
	}

	if cmds == nil {
		return NoneEntry(), nil
	}

	entry := entry.NewEntry(keyStr)
	entry.AddPayload(cmds...)
	return SomeEntry(entry), nil
}

func (cpec *CheckpointDecoder) handleQueueDecode(rawQueMetaKey, rawQueMetaVal []byte) (entryOpt EntryOpt, err error) {
	queMeta, err := parser.ParseQueueMeta(rawQueMetaKey, rawQueMetaVal)
	if err != nil {
		return
	}
	if queMeta.Size == 0 {
		return
	}

	key := queMeta.Key
	keyStr := string(key)

	// inclusive
	lowerSeq := queMeta.Left
	// exclusive
	upperSeq := queMeta.Right + 1

	itermIter := func() (parser.ListVal, bool) {
		if lowerSeq >= upperSeq {
			return nil, false
		}

		curSeq := lowerSeq
		lowerSeq += 1

		itemKey := buildQueItemKey(queMeta, curSeq)
		var rawValue *[]byte
		rawValue, err = cpec.DB.Get(r2.R2CF_List, itemKey)
		if err != nil {
			err = utils.Errorf("failed to get queue item for key %s: %w", utils.EscapeBytes(itemKey), err)
			return nil, false
		}
		if rawValue == nil {
			err = utils.Errorf("queue item for key %s not found", utils.EscapeBytes(itemKey))
			return nil, false
		}

		var queItem *parser.QueueItem
		queItem, err = parser.ParseQueueItem(itemKey, *rawValue)
		if err != nil {
			return nil, false
		}

		return queItem, true
	}

	cmds := cpec.list2Cmd(keyStr, itermIter)
	if err != nil {
		// the err is emitted by the itermIter function
		return
	}

	if cmds == nil {
		return NoneEntry(), nil
	}

	entry := entry.NewEntry(keyStr)
	entry.AddPayload(cmds...)
	return SomeEntry(entry), nil
}
