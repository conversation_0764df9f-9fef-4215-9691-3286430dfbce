package checkpointdecoder

import (
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/entry"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2"
	"git.xiaojukeji.com/fusion/fusion-syncer/internal/r2/parser"
	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
)

func (cpec *CheckpointDecoder) processSet(firstRawKey, key []byte) (entryOpt EntryOpt, err error) {
	keyStr := string(key)
	upperBound := cpec.buildKeyUpperBound(key)
	iter, err := cpec.DB.NewIterator(r2.R2CF_Set, upperBound)
	if err != nil {
		err = utils.Errorf("failed to create iterator for set with key %s: %w", keyStr, err)
		return
	}
	defer iter.Close()

	iter.Seek(firstRawKey)

	var itemIter func() (*parser.SetItem, bool)
	itemIter = func() (*parser.SetItem, bool) {
		if !iter.Valid() {
			return nil, false
		}

		rawKey := iter.Key().Data()
		rawVal := iter.Value().Data()

		var setItem *parser.SetItem
		setItem, err = parser.ParseSetItem(rawKey, rawVal)

		if err != nil {
			return nil, false
		}

		if setItem.Deleted {
			iter.Next()
			return itemIter()
		}

		setItem.Key = utils.CopySlice(setItem.Key)
		setItem.Member = utils.CopySlice(setItem.Member)

		iter.Next()
		return setItem, true
	}

	cmds := cpec.set2Cmd(keyStr, itemIter)
	if err != nil {
		return
	}

	if cmds == nil {
		return NoneEntry(), nil
	}

	entry := entry.NewEntry(keyStr)
	entry.AddPayload(cmds...)
	return SomeEntry(entry), nil
}

func (cpec *CheckpointDecoder) processHash(firstRawKey, key []byte) (entryOpt EntryOpt, err error) {
	keyStr := string(key)
	upperBound := cpec.buildKeyUpperBound(key)
	iter, err := cpec.DB.NewIterator(r2.R2CF_HashSet, upperBound)
	if err != nil {
		err = utils.Errorf("failed to create iterator for hash with key %s: %w", keyStr, err)
		return
	}
	defer iter.Close()

	iter.Seek(firstRawKey)
	var itemIter func() (*parser.HashItem, bool)

	itemIter = func() (*parser.HashItem, bool) {
		if !iter.Valid() {
			return nil, false
		}

		rawKey := iter.Key().Data()
		rawVal := iter.Value().Data()

		var hashItem *parser.HashItem

		hashItem, err = parser.ParseHashItem(rawKey, rawVal)
		if err != nil {
			return nil, false
		}

		fieldVal, ok := hashItem.Value.Get()

		if !ok {
			// If the value is not present, it means the field is deleted
			// Skip this one
			iter.Next()
			return itemIter()
		}

		hashItem.Key = utils.CopySlice(hashItem.Key)
		hashItem.Field = utils.CopySlice(hashItem.Field)
		hashItem.Value = parser.SomeBytes(utils.CopySlice(fieldVal))

		iter.Next()
		return hashItem, true
	}

	cmds := cpec.hash2Cmd(keyStr, itemIter)
	if err != nil {
		return
	}

	if cmds == nil {
		return NoneEntry(), nil
	}

	entry := entry.NewEntry(keyStr)
	entry.AddPayload(cmds...)
	return SomeEntry(entry), nil
}

func (cpec *CheckpointDecoder) processZSet(firstRawKey, key []byte) (entryOpt EntryOpt, err error) {
	keyStr := string(key)
	upperBound := cpec.buildKeyUpperBound(key)
	iter, err := cpec.DB.NewIterator(r2.R2CF_ZSet, upperBound)
	if err != nil {
		err = utils.Errorf("failed to create iterator for zset with key %s: %w", keyStr, err)
		return
	}
	defer iter.Close()

	iter.Seek(firstRawKey)

	var itemIter func() (*parser.ZSetItem, bool)
	itemIter = func() (*parser.ZSetItem, bool) {
		if !iter.Valid() {
			return nil, false
		}

		rawKey := iter.Key().Data()
		rawVal := iter.Value().Data()

		if rawKey[len(rawKey)-1] != 's' {
			// This is score key, skip it
			iter.Next()
			return itemIter()
		}

		var zsetItem *parser.ZSetItem
		zsetItem, err = parser.ParseZsetItem(rawKey, rawVal)
		if err != nil {
			return nil, false
		}

		if zsetItem.Score.IsAbsent() {
			// If the score is absent, it means the member is deleted
			// Skip this one
			iter.Next()
			return itemIter()
		}

		// rawKey and rawVal are from C heap, so we need to copy them
		zsetItem.Key = utils.CopySlice(zsetItem.Key)
		zsetItem.Member = utils.CopySlice(zsetItem.Member)
		iter.Next()

		return zsetItem, true
	}

	cmds := cpec.zset2Cmd(keyStr, itemIter)
	if err != nil {
		return
	}

	if cmds == nil {
		return NoneEntry(), nil
	}

	entry := entry.NewEntry(keyStr)
	entry.AddPayload(cmds...)
	return SomeEntry(entry), nil
}
