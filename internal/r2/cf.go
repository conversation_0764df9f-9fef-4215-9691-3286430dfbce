package r2

import "fmt"

/*
#[repr(u32)]
pub enum R2CF {
    TTL = 1,
    String = 2,
    HashSet = 3,
    ZSet = 4,
    List = 5,
    Set = 6,
    Max = 7,
}
*/

type R2CF uint32

const (
	R2CF_TTL     R2CF = 1
	R2CF_String  R2CF = 2
	R2CF_HashSet R2CF = 3
	R2CF_ZSet    R2CF = 4
	R2CF_List    R2CF = 5
	R2CF_Set     R2CF = 6
	R2CF_Max     R2CF = 7
)

func (c R2CF) String() string {
	switch c {
	case R2CF_TTL:
		return "cf-ttl"
	case R2CF_String:
		return "cf-string"
	case R2CF_HashSet:
		return "cf-hashset"
	case R2CF_ZSet:
		return "cf-zset"
	case R2CF_List:
		return "cf-list"
	case R2CF_Set:
		return "cf-set"
	case R2CF_Max:
		return "cf-max"
	}
	return fmt.Sprintf("cf-unknown(%d)", c)
}

func R2CFVariants() []R2CF {
	return []R2CF{
		R2CF_TTL,
		R2CF_String,
		R2CF_HashSet,
		R2CF_ZSet,
		R2CF_List,
		R2CF_Set,
	}
}

func R2CFIsValid(cf R2CF) bool {
	switch cf {
	case R2CF_TTL, R2CF_String, R2CF_HashSet, R2CF_ZSet, R2CF_List, R2CF_Set, R2CF_Max:
		return true
	}
	return false
}

func R2CFNames() []string {
	names := make([]string, len(R2CFVariants()))
	for i, cf := range R2CFVariants() {
		names[i] = cf.String()
	}
	return names
}

func R2CFFromPrimitive(cf uint32) (R2CF, error) {
	if cf < uint32(R2CF_TTL) || cf > uint32(R2CF_Max) {
		return 0, fmt.Errorf("invalid R2CF value: %d", cf)
	}
	return R2CF(cf), nil
}
