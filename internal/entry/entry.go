package entry

import (
	"fmt"
	"strconv"
	"strings"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/logger"
)

// todo: may be we can add some generic restrictions on the tokens
type Command struct {
	Tokens   []any
	KeyPos   int // position of the key in the Tokens slice
	DataSize int
	Seq      uint64 // sequence number of the command

	// decoratedWithTable bool   // indicates if the key has been decorated with a table prefix
}

func (cp *Command) GetTokens(tableName string) (tokens []any) {
	tokens = cp.Tokens
	if len(cp.Tokens) < 2 {
		return
	}

	if cp.KeyPos == 0 {
		logger.Warnf("command key position is 0, this is not expected, command: %v", cp)
		return
	}

	if cp.KeyPos >= len(cp.Tokens) {
		logger.Errorf("command key position %d is out of bounds for tokens length %d, command: %s", cp.KeyPos, len(cp.Tokens), cp)
		return cp.Tokens
	}

	// if !cp.decoratedWithTable {
	// 	cp.decoratedWithTable = true
	// 	// decorate the key with the table name
	// 	cp.decorateTable(tableName)
	// }

	return cp.Tokens
}

func (cp *Command) String() string {
	if len(cp.Tokens) == 0 {
		return "<empty command>"
	}

	sb := strings.Builder{}
	sb.WriteString("Command{ KeyPos: " + strconv.Itoa(cp.KeyPos) + ", Tokens: [")
	for i, token := range cp.Tokens {
		if i > 0 {
			sb.WriteString(" ")
		}
		switch t := token.(type) {
		case string:
			sb.WriteString("\"" + t + "\"")
		case []byte:
			sb.WriteString("\"" + string(t) + "\"")
		case int:
			sb.WriteString(strconv.FormatInt(int64(t), 10))
		case int64:
			sb.WriteString(strconv.FormatInt(t, 10))
		case uint:
			sb.WriteString(strconv.FormatUint(uint64(t), 10))
		case uint64:
			sb.WriteString(strconv.FormatUint(t, 10))
		default:
			sb.WriteString(fmt.Sprintf("<unsupported type: %T, value: %v>", t, t))
		}
	}
	sb.WriteString("] }")
	return sb.String()
}

//lint:ignore U1000 Ignore unsed function, it may be used in the future
// func (cp *Command) decorateTable(tableName string) {
// 	oldKey := ""
// 	switch key := cp.Tokens[cp.KeyPos].(type) {
// 	case string:
// 		oldKey = key
// 	case []byte:
// 		oldKey = string(key)
// 	default:
// 		logger.Errorf("unsupported key type: %T in command: %v", cp.Tokens[cp.KeyPos], cp)
// 		return
// 	}
// 	newKey := tableName + ":" + oldKey
// 	cp.Tokens[cp.KeyPos] = newKey
// 	cp.decoratedWithTable = true
// 	logger.Debugf("decorated command key with table name, old key: %s, new key: %s, command: %v", oldKey, newKey, cp)
// }

type Entry struct {
	Key     string
	Cmds    []Command
	LastSeq uint64
	LogNum  int64

	// to notify the writer that the entry is consumed
	Consumed func()
}

func NewEntry(key string) *Entry {
	return &Entry{
		Key:      key,
		Cmds:     make([]Command, 0, 4), // Preallocate space for cmd lines
		Consumed: func() {},
	}
}

func NewEntryWithSeq(key string, seq uint64, logNum uint64) *Entry {
	entry := NewEntry(key)
	entry.LastSeq = seq
	entry.LogNum = int64(logNum)
	return entry
}

func (e *Entry) AddPayload(payload ...Command) {
	e.Cmds = append(e.Cmds, payload...)
}

func (e *Entry) Merge(other *Entry) {
	if e.Key != other.Key {
		return
	}

	e.Cmds = append(e.Cmds, other.Cmds...)
	e.LastSeq = max(e.LastSeq, other.LastSeq)
	e.LogNum = max(e.LogNum, other.LogNum)
}

func (e *Entry) AddCmd(cmd Command) {
	if e.Key != cmd.Tokens[cmd.KeyPos] {
		logger.Errorf("command key %s does not match entry key %s", cmd.Tokens[cmd.KeyPos], e.Key)
		return
	}

	e.Cmds = append(e.Cmds, cmd)
	e.LastSeq = max(e.LastSeq, cmd.Seq)
}
