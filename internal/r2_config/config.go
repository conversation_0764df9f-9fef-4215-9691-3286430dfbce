package r2config

import (
	"os"
	"strings"

	"git.xiaojukeji.com/fusion/fusion-syncer/pkg/utils"
	"github.com/creasty/defaults"
	"github.com/pelletier/go-toml/v2"
)

type ConnConfig struct {
	DialTimeoutMs  int64 `toml:"dial_timeout_msec" default:"1000"`
	ReadTimeoutMs  int64 `toml:"read_timeout_msec" default:"1000"`
	WriteTimeoutMs int64 `toml:"write_timeout_msec" default:"1000"`
}

type AuthConfig struct {
	Username string `toml:"username"`
	Password string `toml:"password"`
}

type SourceConfig struct {
	Conn ConnConfig `toml:"conn"`
	Addr string     `toml:"source_addr"`

	ReadTimeoutSec int64  `toml:"read_timeout_sec" default:"5"`
	ServerID       string `toml:"server_id"`
	UseQueue       bool   `toml:"use_queue" default:"true"`
}

type TargetConfig struct {
	Conn        ConnConfig `toml:"conn"`
	Addr        string     `toml:"dst_address"`
	Ns          string     `toml:"dst_namespace" default:"fusion"`
	ClusterName string     `toml:"dst_cluster_name"`
	Auth        AuthConfig `toml:"auth"`

	Parallel             int   `toml:"write_parallel" default:"16"`
	WriteRetry           int   `toml:"write_retry_times" default:"3"`
	MaxRedirectTimes     int   `toml:"write_redirect_times" default:"3"`
	PipelineWriteBatch   int32 `toml:"pipeline_write_batch" default:"1000"`
	PipelineWriteDelayMs int64 `toml:"pipeline_write_delay_ms" default:"100"`
}

type GeneralConfig struct {
	Dir           string `toml:"dir" default:"data"`
	ServePort     int    `toml:"serve_port"`
	PprofPort     int    `toml:"pprof_port"`
	LogDir        string `toml:"log_dir" default:"logs"`
	LogLevel      string `toml:"log_level" default:"debug"`
	CkptBatchSize int    `toml:"ckpt_batch_size" default:"2"`
	MemLimitMiB   int64  `toml:"mem_limit_mib" default:"1024"` // 1 GiB
}

type Config struct {
	General GeneralConfig `toml:"general"`
	Source  SourceConfig  `toml:"source"`
	Target  TargetConfig  `toml:"target"`
}

type RateLimitConfig struct {
	TableName string `toml:"table_name"`
	MiBPerSec int64  `toml:"writer_rate_limiter_mbps" default:"80"`
}

func LoadConfig(configFile string) (Config, error) {
	config := Config{}

	if err := defaults.Set(&config); err != nil {
		return config, err
	}

	tomlFile, err := os.Open(configFile)
	if err != nil {
		return config, err
	}
	defer tomlFile.Close()
	// return config, toml.NewDecoder(tomlFile).Decode(&config)
	if err := toml.NewDecoder(tomlFile).Decode(&config); err != nil {
		return config, err
	}

	if !strings.HasPrefix(config.Target.Addr, "http://") {
		config.Target.Addr = "http://" + config.Target.Addr
	}

	// make a validator here
	if config.Target.Parallel <= 0 {
		return config, utils.Errorf("target.parallel must be greater than 0, got %d", config.Target.Parallel)
	}

	return config, nil
}
