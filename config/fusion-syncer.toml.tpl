# Fusion Syncer Configuration Template

[general]
dir = "data"
serve_port = {{serve_port}} # Api server port, int type
pprof_port = {{pprof_port}} # Pprof port, int type
log_dir = "log"

[source]
source_addr = "{{source_addr}}" # Source r2 address, string type, like "localhost:6379"
read_timeout_sec = 5 
server_id = {{server_id}} # ServerID, string type
use_queue = true


[source.conn]
dial_timeout_msec = 1000 
read_timeout_msec = 1000 
write_timeout_msec = 1000 

[target]
dst_address = "http://{{target_addr}}" # Target kvrocks controller address, string type, like xxxx:8080
dst_cluster_name = "{{dst_cluster_name}}"

write_parallel = {{parallel}} # Number of parallel connections to target, int type
write_retry_times = 3
write_redirect_times = 3
pipeline_write_batch = 1000
pipeline_write_delay_ms = 1000

[target.conn]
dial_timeout_msec = 1000  
read_timeout_msec = 1000  
write_timeout_msec = 1000

[target.auth]
username = {{target_username}} # Target kvrocks proxy username, string type
password = {{target_password}} # Target kvrocks proxy password, string type