[general]
dir = "data"
serve_port = 4000
pprof_port = 14000
log_dir = "logs"
# log_level = "info"

[source]
source_addr = "*************:6001"
read_timeout_sec = 5
server_id = "1"
use_queue = true

[source.conn]
dial_timeout_msec = 1000
read_timeout_msec = 1000
write_timeout_msec = 1000

[target]
dst_address = "*************:9379"
dst_namespace = "kvrocks"
dst_cluster_name = "test-cluster"

write_parallel = 16
write_retry_times = 3
write_redirect_times = 3
pipeline_write_batch = 32
pipeline_write_delay_ms = 100

[target.conn]
dial_timeout_msec = 1000
read_timeout_msec = 3000
write_timeout_msec = 3000

[target.auth]
username = ""
password = ""